/*************************************************************************
 * Copyright (c) 2024, NVIDIA CORPORATION. All rights reserved.
 *
 * See LICENSE.txt for license information
 ************************************************************************/

/**
 * @file plugin.h
 * @brief NCCL Profiler 示例插件外部接口头文件
 *
 * 本文件定义了 NCCL Profiler 示例插件的外部控制接口。
 * 提供了启动和停止 profiler 的函数声明，允许外部应用程序
 * 动态控制性能分析的开启和关闭。
 *
 * 主要功能：
 * - 提供 profiler 启动控制接口
 * - 提供 profiler 停止控制接口
 * - 支持动态事件激活掩码配置
 * - 为外部应用程序提供简洁的控制 API
 */

#ifndef PLUGIN_H_
#define PLUGIN_H_

/**
 * @brief 启动示例 profiler 并设置事件激活掩码
 *
 * 启动 NCCL profiler 的性能分析功能，并设置要跟踪的事件类型。
 * 事件激活掩码决定了哪些类型的 NCCL 事件会被 profiler 捕获和记录。
 *
 * @param eActivationMask 事件激活掩码：位掩码，指定要激活的事件类型
 *                        - 可以是 ncclProfileGroup、ncclProfileColl、ncclProfileP2p 等的组合
 *                        - 使用按位或操作组合多个事件类型
 *                        - 0 表示禁用所有事件跟踪
 *
 * @return ncclSuccess 表示成功启动，其他值表示错误
 *
 * @note 只有在 profiler 已经初始化的情况下才会生效
 * @note 可以在运行时多次调用以动态调整跟踪的事件类型
 */
int exampleProfilerStart(int eActivationMask);

/**
 * @brief 停止示例 profiler 的事件跟踪
 *
 * 停止 NCCL profiler 的性能分析功能，不再捕获和记录任何事件。
 * 相当于将事件激活掩码设置为 0。
 *
 * @return ncclSuccess 表示成功停止，其他值表示错误
 *
 * @note 只有在 profiler 已经初始化的情况下才会生效
 * @note 停止后可以通过 exampleProfilerStart 重新启动事件跟踪
 * @note 不会影响已经收集的性能数据，只是停止新事件的收集
 */
int exampleProfilerStop(void);

#endif
