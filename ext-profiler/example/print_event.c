/*************************************************************************
 * Copyright (c) 2024, NVIDIA CORPORATION. All rights reserved.
 *
 * See LICENSE.txt for license information
 ************************************************************************/

/**
 * @file print_event.c
 * @brief NCCL Profiler 示例插件事件打印功能实现
 *
 * 本文件实现了将 NCCL Profiler 收集的性能事件数据格式化为 Chrome 跟踪格式
 * JSON 输出的完整功能。支持所有类型的 NCCL 事件，包括组事件、集合操作、
 * 点对点操作、代理操作、代理步骤、内核通道事件和网络插件事件。
 *
 * 主要功能：
 * - 实现 Chrome 跟踪格式兼容的 JSON 输出
 * - 支持异步事件格式，包含开始和结束时间戳
 * - 提供递归的事件层次结构打印
 * - 支持多种网络插件事件（InfiniBand、Socket）
 * - 提供详细的调试信息输出功能
 * - 使用线程局部存储管理事件 ID，确保多线程安全
 *
 * Chrome 跟踪格式说明：
 * - 使用异步事件格式（"ph": "b" 表示开始，"ph": "e" 表示结束）
 * - 每个事件都有唯一的 ID 和类别，避免嵌套冲突
 * - 支持事件参数（args）传递详细的性能数据
 * - 兼容 Chrome 浏览器的 chrome://tracing 可视化工具
 */

#include <stdio.h>
#include "profiler.h"
#include "event.h"
#include "print_event.h"

/* 编译器属性定义 */
#define __hidden __attribute__ ((visibility("hidden")))  // 隐藏符号，仅内部使用

/* Chrome 跟踪格式兼容性说明 */
// 注意：Chrome 跟踪异步事件（以下使用的格式）允许具有相同 ID 和类别的事件嵌套
// 但是嵌套超过三个事件会导致问题。因此，每个事件都被分配一个递增的 ID 和
// 与事件类型匹配的类别（GROUP、COLL、P2P、PROXY、NET、GPU）

/* 线程局部存储的事件 ID 计数器 */
// 使用线程局部存储确保多线程环境下的 ID 唯一性
static __thread int groupId;        // 组事件 ID 计数器
/**
 * @brief 打印组事件的开始 JSON 记录
 *
 * 输出组事件开始的 Chrome 跟踪格式 JSON 记录。组事件是 NCCL 操作的
 * 最高层次容器，包含一个或多个集合操作或点对点操作。
 *
 * @param fh 文件句柄，指向输出文件流
 * @param event 组事件指针，包含要输出的组事件数据
 *
 * @note 使用 "GROUP" 类别和 "b"（begin）阶段标识符
 * @note 包含组 ID 作为参数，便于跟踪和调试
 */
__hidden void printGroupEventHeader(FILE* fh, struct group* event) {
  fprintf(fh, "{\"name\": \"%s\", \"cat\": \"GROUP\", \"ph\": \"b\", \"id\": %d, \"pid\": %d, \"tid\": %d, \"ts\": %f, \"args\": {\"groupId\": %d}},\n",
          "Group", groupId, getpid(), 1, event->startTs, event->groupId);
}

/**
 * @brief 打印组事件的结束 JSON 记录
 *
 * 输出组事件结束的 Chrome 跟踪格式 JSON 记录，与开始记录配对形成
 * 完整的异步事件。
 *
 * @param fh 文件句柄，指向输出文件流
 * @param event 组事件指针，包含要输出的组事件数据
 *
 * @note 使用 "GROUP" 类别和 "e"（end）阶段标识符
 * @note 递增 groupId 计数器，为下一个组事件准备唯一 ID
 */
__hidden void printGroupEventTrailer(FILE* fh, struct group* event) {
  fprintf(fh, "{\"name\": \"%s\", \"cat\": \"GROUP\", \"ph\": \"e\", \"id\": %d, \"pid\": %d, \"tid\": %d, \"ts\": %f},\n",
          "Group", groupId++, getpid(), 1, event->stopTs);
}

static __thread int collId;            // 集合操作事件 ID 计数器

/**
 * @brief 打印集合操作事件的开始 JSON 记录
 *
 * 输出集合操作事件开始的 Chrome 跟踪格式 JSON 记录。集合操作包括
 * AllReduce、AllGather、Broadcast 等 NCCL 集合通信操作。
 *
 * @param fh 文件句柄，指向输出文件流
 * @param event 集合操作事件指针，包含要输出的集合操作数据
 *
 * @note 使用 "COLL" 类别和 "b"（begin）阶段标识符
 * @note 包含丰富的参数信息：序列号、通信器哈希、排名、数据计数、数据类型、算法、协议、通道数
 * @note 事件名称使用实际的 NCCL 函数名（如 "AllReduce"、"Broadcast"）
 */
__hidden void printCollEventHeader(FILE* fh, struct collective* event) {
  fprintf(fh, "{\"name\": \"%s\", \"cat\": \"COLL\", \"ph\": \"b\", \"id\": %d, \"pid\": %d, \"tid\": %d, \"ts\": %f, \"args\": {\"SeqNum\": %lu, \"CommHash\": %lu, \"Rank\": %d, \"Count\": %lu, \"Datatype\": \"%s\", \"Algorithm\": \"%s\", \"Protocol\": \"%s\", \"nChannels\": %d}},\n",
          event->base.func, collId, getpid(), 1, event->base.startTs, event->seqNumber, event->base.parent->ctx->commHash, event->base.rank, event->count, event->datatype, event->algo, event->proto, event->nChannels);
}

/**
 * @brief 打印集合操作事件的结束 JSON 记录
 *
 * 输出集合操作事件结束的 Chrome 跟踪格式 JSON 记录，与开始记录配对
 * 形成完整的异步事件。
 *
 * @param fh 文件句柄，指向输出文件流
 * @param event 集合操作事件指针，包含要输出的集合操作数据
 *
 * @note 使用 "COLL" 类别和 "e"（end）阶段标识符
 * @note 递增 collId 计数器，为下一个集合操作事件准备唯一 ID
 */
__hidden void printCollEventTrailer(FILE* fh, struct collective* event) {
  fprintf(fh, "{\"name\": \"%s\", \"cat\": \"COLL\", \"ph\": \"e\", \"id\": %d, \"pid\": %d, \"tid\": %d, \"ts\": %f},\n",
          event->base.func, collId++, getpid(), 1, event->base.stopTs);
}

static __thread int p2pId;             // 点对点操作事件 ID 计数器

/**
 * @brief 打印点对点操作事件的开始 JSON 记录
 *
 * 输出点对点操作事件开始的 Chrome 跟踪格式 JSON 记录。点对点操作包括
 * Send、Recv 等 NCCL 点对点通信操作。
 *
 * @param fh 文件句柄，指向输出文件流
 * @param event 点对点操作事件指针，包含要输出的点对点操作数据
 *
 * @note 使用 "P2P" 类别和 "b"（begin）阶段标识符
 * @note 包含关键参数：通信器哈希、排名、对等方排名、数据计数、数据类型、通道数
 * @note 事件名称使用实际的 NCCL 函数名（如 "Send"、"Recv"）
 */
__hidden void printP2pEventHeader(FILE* fh, struct p2p* event) {
  fprintf(fh, "{\"name\": \"%s\", \"cat\": \"P2P\", \"ph\": \"b\", \"id\": %d, \"pid\": %d, \"tid\": %d, \"ts\": %f, \"args\": {\"CommHash\": %lu, \"Rank\": %d, \"Peer\": %d, \"Count\": %lu, \"Datatype\": \"%s\", \"nChannels\": %d}},\n",
          event->base.func, p2pId, getpid(), 1, event->base.startTs, event->base.parent->ctx->commHash, event->base.rank, event->peer, event->count, event->datatype, event->nChannels);
}

/**
 * @brief 打印点对点操作事件的结束 JSON 记录
 *
 * 输出点对点操作事件结束的 Chrome 跟踪格式 JSON 记录，与开始记录配对
 * 形成完整的异步事件。
 *
 * @param fh 文件句柄，指向输出文件流
 * @param event 点对点操作事件指针，包含要输出的点对点操作数据
 *
 * @note 使用 "P2P" 类别和 "e"（end）阶段标识符
 * @note 递增 p2pId 计数器，为下一个点对点操作事件准备唯一 ID
 */
__hidden void printP2pEventTrailer(FILE* fh, struct p2p* event) {
  fprintf(fh, "{\"name\": \"%s\", \"cat\": \"P2P\", \"ph\": \"e\", \"id\": %d, \"pid\": %d, \"tid\": %d, \"ts\": %f},\n",
          event->base.func, p2pId++, getpid(), 1, event->base.stopTs);
}

static __thread int proxyOpId;          // 代理操作事件 ID 计数器

/**
 * @brief 打印代理操作事件的开始 JSON 记录
 *
 * 输出代理操作事件开始的 Chrome 跟踪格式 JSON 记录。代理操作分为两个阶段：
 * 调度阶段（Schedule）和进度阶段（Progress），分别对应操作的入队和实际执行。
 *
 * @param fh 文件句柄，指向输出文件流
 * @param event 代理操作事件指针，包含要输出的代理操作数据
 *
 * @note 使用 "PROXY" 类别和 "b"/"e" 阶段标识符
 * @note 根据 isSend 标志区分发送和接收操作
 * @note 输出两个连续的事件：Schedule（调度）和 Progress（进度）
 * @note 包含详细参数：通道 ID、对等方、步骤数、块大小、传输大小
 */
__hidden void printProxyOpEventHeader(FILE* fh, struct proxyOp* event) {
  if (event->isSend) {
    /* 发送操作：输出调度发送和进度发送两个阶段 */
    // 调度发送阶段：从开始时间到进度时间
    fprintf(fh, "{\"name\": \"%s\", \"cat\": \"PROXY\", \"ph\": \"b\", \"id\": %d, \"pid\": %d, \"tid\": %d, \"ts\": %f, \"args\": {\"Channel\": %d, \"Peer\": %d, \"Steps\": %d, \"ChunkSize\": %d, \"transSize\": %lu}},\n",
            "ScheduleSend", proxyOpId, getpid(), 1, event->startTs, event->channelId, event->peer, event->nSteps, event->chunkSize, event->transSize);
    fprintf(fh, "{\"name\": \"%s\", \"cat\": \"PROXY\", \"ph\": \"e\", \"id\": %d, \"pid\": %d, \"tid\": %d, \"ts\": %f},\n",
            "ScheduleSend", proxyOpId, getpid(), 1, event->progrTs);
    // 进度发送阶段：从进度时间开始
    fprintf(fh, "{\"name\": \"%s\", \"cat\": \"PROXY\", \"ph\": \"b\", \"id\": %d, \"pid\": %d, \"tid\": %d, \"ts\": %f, \"args\": {\"Channel\": %d, \"Peer\": %d, \"Steps\": %d, \"ChunkSize\": %d, \"transSize\": %lu}},\n",
            "ProgressSend", proxyOpId, getpid(), 1, event->progrTs, event->channelId, event->peer, event->nSteps, event->chunkSize, event->transSize);
  } else {
    /* 接收操作：输出调度接收和进度接收两个阶段 */
    // 调度接收阶段：从开始时间到进度时间
    fprintf(fh, "{\"name\": \"%s\", \"cat\": \"PROXY\", \"ph\": \"b\", \"id\": %d, \"pid\": %d, \"tid\": %d, \"ts\": %f, \"args\": {\"Channel\": %d, \"Peer\": %d, \"Steps\": %d, \"ChunkSize\": %d, \"transSize\": %lu}},\n",
            "ScheduleRecv", proxyOpId, getpid(), 1, event->startTs, event->channelId, event->peer, event->nSteps, event->chunkSize, event->transSize);
    fprintf(fh, "{\"name\": \"%s\", \"cat\": \"PROXY\", \"ph\": \"e\", \"id\": %d, \"pid\": %d, \"tid\": %d, \"ts\": %f},\n",
            "ScheduleRecv", proxyOpId, getpid(), 1, event->progrTs);
    // 进度接收阶段：从进度时间开始
    fprintf(fh, "{\"name\": \"%s\", \"cat\": \"PROXY\", \"ph\": \"b\", \"id\": %d, \"pid\": %d, \"tid\": %d, \"ts\": %f, \"args\": {\"Channel\": %d, \"Peer\": %d, \"Steps\": %d, \"ChunkSize\": %d, \"transSize\": %lu}},\n",
            "ProgressRecv", proxyOpId, getpid(), 1, event->progrTs, event->channelId, event->peer, event->nSteps, event->chunkSize, event->transSize);
  }
}

/**
 * @brief 打印代理操作事件的结束 JSON 记录
 *
 * 输出代理操作事件结束的 Chrome 跟踪格式 JSON 记录，完成进度阶段的事件。
 *
 * @param fh 文件句柄，指向输出文件流
 * @param event 代理操作事件指针，包含要输出的代理操作数据
 *
 * @note 使用 "PROXY" 类别和 "e"（end）阶段标识符
 * @note 根据 isSend 标志选择对应的进度事件名称
 * @note 递增 proxyOpId 计数器，为下一个代理操作事件准备唯一 ID
 */
__hidden void printProxyOpEventTrailer(FILE* fh, struct proxyOp* event) {
  fprintf(fh, "{\"name\": \"%s\", \"cat\": \"PROXY\", \"ph\": \"e\", \"id\": %d, \"pid\": %d, \"tid\": %d, \"ts\": %f},\n",
          event->isSend ? "ProgressSend" : "ProgressRecv", proxyOpId++, getpid(), 1, event->stopTs);
}

static __thread int proxyStepId;        // 代理步骤事件 ID 计数器

/**
 * @brief 打印代理步骤事件的开始 JSON 记录
 *
 * 输出代理步骤事件开始的 Chrome 跟踪格式 JSON 记录。代理步骤表示单个
 * 网络传输步骤，包含多个状态转换（GPU 等待、对等等待、网络等待等）。
 *
 * @param fh 文件句柄，指向输出文件流
 * @param event 代理步骤事件指针，包含要输出的代理步骤数据
 *
 * @note 使用 "NET" 类别，表示网络层面的操作
 * @note 发送操作包含三个阶段：SendGpuWait、SendPeerWait、SendWait
 * @note 接收操作从 RecvWait 开始，其他阶段在 trailer 函数中输出
 * @note 每个阶段都有对应的时间戳，提供细粒度的性能分析
 */
__hidden void printProxyStepEventHeader(FILE* fh, struct proxyStep* event) {
  if (event->isSend) {
    /* 发送操作：输出三个连续的等待阶段 */
    // GPU 等待阶段：等待 GPU 提供发送数据
    fprintf(fh, "{\"name\": \"%s\", \"cat\": \"NET\", \"ph\": \"b\", \"id\": %d, \"pid\": %d, \"tid\": %d, \"ts\": %f, \"args\": {\"Step\": %d}},\n",
            "SendGpuWait", proxyStepId, getpid(), 1, event->timestamp[PROXY_STEP_SEND_GPU_WAIT], event->step);
    fprintf(fh, "{\"name\": \"%s\", \"cat\": \"NET\", \"ph\": \"e\", \"id\": %d, \"pid\": %d, \"tid\": %d, \"ts\": %f},\n",
            "SendGpuWait", proxyStepId, getpid(), 1, event->timestamp[PROXY_STEP_SEND_PEER_WAIT]);
    // 对等等待阶段：等待对等方的接收准备信号
    fprintf(fh, "{\"name\": \"%s\", \"cat\": \"NET\", \"ph\": \"b\", \"id\": %d, \"pid\": %d, \"tid\": %d, \"ts\": %f, \"args\": {\"Step\": %d}},\n",
            "SendPeerWait", proxyStepId, getpid(), 1, event->timestamp[PROXY_STEP_SEND_PEER_WAIT], event->step);
    fprintf(fh, "{\"name\": \"%s\", \"cat\": \"NET\", \"ph\": \"e\", \"id\": %d, \"pid\": %d, \"tid\": %d, \"ts\": %f},\n",
            "SendPeerWait", proxyStepId, getpid(), 1, event->timestamp[PROXY_STEP_SEND_WAIT]);
    // 发送等待阶段：等待网络发送操作完成
    fprintf(fh, "{\"name\": \"%s\", \"cat\": \"NET\", \"ph\": \"b\", \"id\": %d, \"pid\": %d, \"tid\": %d, \"ts\": %f, \"args\": {\"Step\": %d}},\n",
            "SendWait", proxyStepId, getpid(), 1, event->timestamp[PROXY_STEP_SEND_WAIT], event->step);
  } else {
    /* 接收操作：从接收等待开始 */
    // 接收等待阶段：等待网络接收操作完成
    fprintf(fh, "{\"name\": \"%s\", \"cat\": \"NET\", \"ph\": \"b\", \"id\": %d, \"pid\": %d, \"tid\": %d, \"ts\": %f, \"args\": {\"Step\": %d}},\n",
            "RecvWait", proxyStepId, getpid(), 1, event->timestamp[PROXY_STEP_RECV_WAIT], event->step);
  }
}

/**
 * @brief 打印代理步骤事件的结束 JSON 记录
 *
 * 输出代理步骤事件结束的 Chrome 跟踪格式 JSON 记录。对于发送操作，
 * 只需结束最后的 SendWait 阶段；对于接收操作，需要输出剩余的两个阶段。
 *
 * @param fh 文件句柄，指向输出文件流
 * @param event 代理步骤事件指针，包含要输出的代理步骤数据
 *
 * @note 使用 "NET" 类别和相应的阶段标识符
 * @note 发送操作：结束 SendWait 阶段
 * @note 接收操作：完成 RecvWait，然后输出 RecvFlushWait 和 RecvGpuWait 阶段
 * @note 递增 proxyStepId 计数器，为下一个代理步骤事件准备唯一 ID
 */
__hidden void printProxyStepEventTrailer(FILE* fh, struct proxyStep* event) {
  if (event->isSend) {
    /* 发送操作：结束最后的发送等待阶段 */
    fprintf(fh, "{\"name\": \"%s\", \"cat\": \"NET\", \"ph\": \"e\", \"id\": %d, \"pid\": %d, \"tid\": %d, \"ts\": %f},\n",
            "SendWait", proxyStepId++, getpid(), 1, event->stopTs);
  } else {
    /* 接收操作：完成剩余的三个阶段 */
    // 结束接收等待阶段
    fprintf(fh, "{\"name\": \"%s\", \"cat\": \"NET\", \"ph\": \"e\", \"id\": %d, \"pid\": %d, \"tid\": %d, \"ts\": %f},\n",
            "RecvWait", proxyStepId, getpid(), 1, event->timestamp[PROXY_STEP_RECV_FLUSH_WAIT]);
    // 接收刷新等待阶段：等待接收数据刷新到 GPU 内存
    fprintf(fh, "{\"name\": \"%s\", \"cat\": \"NET\", \"ph\": \"b\", \"id\": %d, \"pid\": %d, \"tid\": %d, \"ts\": %f, \"args\": {\"Step\": %d}},\n",
            "RecvFlushWait", proxyStepId, getpid(), 1, event->timestamp[PROXY_STEP_RECV_FLUSH_WAIT], event->step);
    fprintf(fh, "{\"name\": \"%s\", \"cat\": \"NET\", \"ph\": \"e\", \"id\": %d, \"pid\": %d, \"tid\": %d, \"ts\": %f},\n",
            "RecvFlushWait", proxyStepId, getpid(), 1, event->timestamp[PROXY_STEP_RECV_GPU_WAIT]);
    // 接收 GPU 等待阶段：等待 GPU 消费接收到的数据
    fprintf(fh, "{\"name\": \"%s\", \"cat\": \"NET\", \"ph\": \"b\", \"id\": %d, \"pid\": %d, \"tid\": %d, \"ts\": %f, \"args\": {\"Step\": %d}},\n",
            "RecvGpuWait", proxyStepId, getpid(), 1, event->timestamp[PROXY_STEP_RECV_GPU_WAIT], event->step);
    fprintf(fh, "{\"name\": \"%s\", \"cat\": \"NET\", \"ph\": \"e\", \"id\": %d, \"pid\": %d, \"tid\": %d, \"ts\": %f},\n",
            "RecvGpuWait", proxyStepId++, getpid(), 1, event->stopTs);
  }
}

static __thread int kernelId;           // 内核通道事件 ID 计数器

/**
 * @brief 打印内核通道事件的开始 JSON 记录
 *
 * 输出内核通道事件开始的 Chrome 跟踪格式 JSON 记录。内核通道事件
 * 表示 GPU 内核在特定通道上的操作，包含 GPU 时钟信息。
 *
 * @param fh 文件句柄，指向输出文件流
 * @param event 内核通道事件指针，包含要输出的内核通道数据
 *
 * @note 使用 "GPU" 类别，表示 GPU 端的操作
 * @note 包含通道 ID 和 GPU 时钟计数作为参数
 * @note 只处理类型为 ncclProfileKernelCh 的事件
 */
__hidden void printKernelChEventHeader(FILE* fh, struct kernelCh* event) {
  if (event->type != ncclProfileKernelCh) return;
  fprintf(fh, "{\"name\": \"%s\", \"cat\": \"GPU\", \"ph\": \"b\", \"id\": %d, \"pid\": %d, \"tid\": %d, \"ts\": %f, \"args\": {\"Channel\": %d, \"StartGpuClk\": %lu, \"StopGpuClk\": %lu}},\n",
          "KernelCh", kernelId, getpid(), 1, event->startTs, event->channelId, event->startGpuClk, event->stopGpuClk);
}

/**
 * @brief 打印内核通道事件的结束 JSON 记录
 *
 * 输出内核通道事件结束的 Chrome 跟踪格式 JSON 记录。
 *
 * @param fh 文件句柄，指向输出文件流
 * @param event 内核通道事件指针，包含要输出的内核通道数据
 *
 * @note 使用 "GPU" 类别和 "e"（end）阶段标识符
 * @note 只处理类型为 ncclProfileKernelCh 的事件
 */
__hidden void printKernelChEventTrailer(FILE* fh, struct kernelCh* event) {
  if (event->type != ncclProfileKernelCh) return;
  fprintf(fh, "{\"name\": \"%s\", \"cat\": \"GPU\", \"ph\": \"e\", \"id\": %d, \"pid\": %d, \"tid\": %d, \"ts\": %f},\n",
          "KernelCh", kernelId, getpid(), 1, event->stopTs);
}

static __thread int proxyCtrlId;        // 代理控制事件 ID 计数器

/**
 * @brief 打印代理控制事件的 JSON 记录
 *
 * 输出代理控制事件的 Chrome 跟踪格式 JSON 记录。代理控制事件表示
 * 代理进度线程的控制状态变化，如空闲、睡眠、追加操作等。
 *
 * @param fh 文件句柄，指向输出文件流
 * @param event 代理控制事件指针，包含要输出的代理控制数据
 *
 * @note 使用 "PROXY" 类别
 * @note 根据状态类型选择相应的事件名称（Idle、Sleep、Append）
 * @note 对于 AppendEnd 状态，包含追加的代理操作数量作为参数
 * @note 输出完整的开始和结束事件对
 */
__hidden void printProxyCtrlEvent(FILE* fh, struct proxyCtrl* event) {
  const char* str;

  /* 根据代理控制状态确定事件名称 */
  if (event->state == ncclProfilerProxyCtrlIdle || event->state == ncclProfilerProxyCtrlActive) {
    str = "Idle";          // 空闲或活动状态
  } else if (event->state == ncclProfilerProxyCtrlSleep || event->state == ncclProfilerProxyCtrlWakeup) {
    str = "Sleep";         // 睡眠或唤醒状态
  } else if (event->state == ncclProfilerProxyCtrlAppend || event->state == ncclProfilerProxyCtrlAppendEnd) {
    str = "Append";        // 追加操作状态
  } else {
    return;                // 未知状态，跳过
  }

  /* 输出开始事件 */
  if (event->state == ncclProfilerProxyCtrlAppendEnd) {
    // 追加结束状态：包含追加的代理操作数量
    fprintf(fh, "{\"name\": \"%s\", \"cat\": \"PROXY\", \"ph\": \"b\", \"id\": %d, \"pid\": %d, \"tid\": %d, \"ts\": %f, \"args\": {\"appended\": %d}},\n",
            str, proxyCtrlId, getpid(), 1, event->startTs, event->appended);
  } else {
    // 其他状态：不包含额外参数
    fprintf(fh, "{\"name\": \"%s\", \"cat\": \"PROXY\", \"ph\": \"b\", \"id\": %d, \"pid\": %d, \"tid\": %d, \"ts\": %f},\n",
            str, proxyCtrlId, getpid(), 1, event->startTs);
  }

  /* 输出结束事件 */
  fprintf(fh, "{\"name\": \"%s\", \"cat\": \"PROXY\", \"ph\": \"e\", \"id\": %d, \"pid\": %d, \"tid\": %d, \"ts\": %f},\n",
          str, proxyCtrlId++, getpid(), 1, event->stopTs);
}

static __thread int ibQpId, sockId;    // 网络插件事件 ID 计数器（InfiniBand QP 和 Socket）

/**
 * @brief 打印网络插件事件的 JSON 记录
 *
 * 输出网络插件事件的 Chrome 跟踪格式 JSON 记录。支持多种网络插件类型，
 * 包括 InfiniBand 和 Socket 网络。每种网络类型都有特定的参数和格式。
 *
 * @param fh 文件句柄，指向输出文件流
 * @param event 网络插件事件指针，包含要输出的网络插件数据
 *
 * @note 根据插件类型使用不同的类别（NET_IB、NET_SOCK）
 * @note 支持版本化的插件接口，当前支持版本 1
 * @note InfiniBand 事件包含设备、QP 号、操作码、工作请求 ID、数据大小等参数
 * @note Socket 事件包含文件描述符、操作类型、数据大小等参数
 */
__hidden void printNetPluginEvent(FILE* fh, struct netPlugin* event) {
  if (event->pluginType == NCCL_PROFILER_NET_TYPE_IB) {
    /* 处理 InfiniBand 网络插件事件 */
    if (event->pluginVer == 1) {
      if (event->pluginEvent == ncclProfileQp) {
        /* InfiniBand QP (Queue Pair) 事件 */
        fprintf(fh, "{\"name\": \"%s\", \"cat\": \"NET_IB\", \"ph\": \"b\", \"id\": %d, \"pid\": %d, \"tid\": %d, \"ts\": %f, \"args\": {\"device\": %d, \"qp_num\": %d, \"opcode\": %d, \"wr_id\": %lu, \"size\": %lu}},\n",
                "Qp", ibQpId, getpid(), 1, event->startTs, event->qp.device, event->qp.qpNum, event->qp.opcode, event->qp.wr_id, event->qp.length);
        fprintf(fh, "{\"name\": \"%s\", \"cat\": \"NET_IB\", \"ph\": \"e\", \"id\": %d, \"pid\": %d, \"tid\": %d, \"ts\": %f},\n",
                "Qp", ibQpId++, getpid(), 1, event->stopTs);
      }
    }
  } else if (event->pluginType == NCCL_PROFILER_NET_TYPE_SOCK) {
    /* 处理 Socket 网络插件事件 */
    if (event->pluginVer == 1) {
      if (event->pluginEvent == ncclProfileSocket) {
        /* Socket 事件 */
        fprintf(fh, "{\"name\": \"%s\", \"cat\": \"NET_SOCK\", \"ph\": \"b\", \"id\": %d, \"pid\": %d, \"tid\": %d, \"ts\": %f, \"args\": {\"sock\": %d, \"op\": %d, \"size\": %lu}},\n",
                "Sock", sockId, getpid(), 1, event->startTs, event->sock.fd, event->sock.op, event->sock.length);
        fprintf(fh, "{\"name\": \"%s\", \"cat\": \"NET_SOCK\", \"ph\": \"e\", \"id\": %d, \"pid\": %d, \"tid\": %d, \"ts\": %f},\n",
                "Sock", sockId++, getpid(), 1, event->stopTs);
      }
    }
  }
}

/* 调试功能开关 */
//#define DEBUG_EVENTS                    // 取消注释以启用调试输出

/**
 * @brief 调试模式下输出事件详细信息
 *
 * 在调试模式下（定义了 DEBUG_EVENTS 宏时）将事件的详细信息输出到
 * 调试文件中。包括事件类型、引用计数、时间戳、父子关系等调试信息。
 *
 * @param eHandle 事件句柄，指向要调试输出的事件对象
 * @param tag 调试标签，用于标识调试输出的上下文（如 "Start", "Stop" 等）
 *
 * @note 只有在编译时定义了 DEBUG_EVENTS 宏才会实际输出调试信息
 * @note 调试信息输出到名为 "EventDebug-{pid}" 的文件中
 * @note 支持所有类型的 NCCL profiler 事件的详细调试输出
 * @note 使用追加模式打开文件，便于跟踪事件的完整生命周期
 */
void debugEvent(void* eHandle, const char* tag) {
#ifdef DEBUG_EVENTS
  char filename[64] = { 0 };
  sprintf(filename, "EventDebug-%d", getpid());           // 创建进程特定的调试文件名
  FILE* fh = fopen(filename, "a+");                       // 以追加模式打开调试文件
  uint8_t type = *(uint8_t *)eHandle;                     // 获取事件类型

  if (type == ncclProfileGroup) {
    /* 调试组事件信息 */
    struct group* event = (struct group *)eHandle;
    fprintf(fh, "Group event %p tag = %s {\n", event, tag);
    fprintf(fh, "  refCount          = %d\n", __atomic_load_n(&event->refCount, __ATOMIC_RELAXED));
    fprintf(fh, "  startTs           = %f\n", event->startTs);
    fprintf(fh, "  stopTs            = %f\n", event->stopTs);
    fprintf(fh, "}\n");

  } else if (type == ncclProfileColl) {
    /* 调试集合操作事件信息 */
    struct collective* event = (struct collective *)eHandle;
    fprintf(fh, "Collective event %p tag = %s {\n", event, tag);
    fprintf(fh, "  refCount          = %d\n", __atomic_load_n(&event->base.refCount, __ATOMIC_RELAXED));
    fprintf(fh, "  parent            = %p\n", event->base.parent);
    // 输出所有活动的代理操作
    for (int j = 0; j < 2*MAX_OPS; j++) {
      for (int i = 0; i < MAX_CHANNELS; i++) {
        if (event->op[i][j].type == ncclProfileProxyOp) {
          fprintf(fh, "  op[%d]           = %p\n", i, &event->op[i]);
        }
      }
    }
    fprintf(fh, "  startTs           = %f\n", event->base.startTs);
    fprintf(fh, "  stopTs            = %f\n", event->base.stopTs);
    fprintf(fh, "}\n");
  } else if (type == ncclProfileP2p) {
    /* 调试点对点操作事件信息 */
    struct p2p* event = (struct p2p *)eHandle;
    fprintf(fh, "P2p event %p tag = %s {\n", event, tag);
    fprintf(fh, "  refCount          = %d\n", __atomic_load_n(&event->base.refCount, __ATOMIC_RELAXED));
    fprintf(fh, "  parent            = %p\n", event->base.parent);
    fprintf(fh, "  op                = %p\n", &event->op);
    fprintf(fh, "  startTs           = %f\n", event->base.startTs);
    fprintf(fh, "  stopTs            = %f\n", event->base.stopTs);
    fprintf(fh, "}\n");

  } else if (type == ncclProfileProxyOp) {
    /* 调试代理操作事件信息 */
    struct proxyOp* event = (struct proxyOp *)eHandle;
    fprintf(fh, "ProxyOp event %p tag = %s {\n", event, tag);
    fprintf(fh, "  type              = %s\n", event->isSend < 0 ? "Unknown" : event->isSend ? "Send" : "Recv");
    fprintf(fh, "  channel           = %d\n", event->channelId);
    fprintf(fh, "  parent            = %p\n", event->parent);
    fprintf(fh, "  rank              = %d\n", event->rank);
    fprintf(fh, "  startTs           = %f\n", event->startTs);
    fprintf(fh, "  progrTs           = %f\n", event->progrTs);
    fprintf(fh, "  stopTs            = %f\n", event->stopTs);
    fprintf(fh, "}\n");

  } else if (type == ncclProfileProxyStep) {
    /* 调试代理步骤事件信息 */
    struct proxyStep* event = (struct proxyStep *)eHandle;
    fprintf(fh, "ProxyStep event %p tag = %s {\n", event, tag);
    fprintf(fh, "  type              = %s\n", event->isSend < 0 ? "Unknown" : event->isSend ? "Send" : "Recv");
    fprintf(fh, "  parent            = %p\n", event->parent);
    fprintf(fh, "  startTs           = %f\n", event->startTs);
    fprintf(fh, "  stopTs            = %f\n", event->stopTs);
    fprintf(fh, "}\n");

  } else if (type == ncclProfileKernelCh) {
    /* 调试内核通道事件信息 */
    struct kernelCh* event = (struct kernelCh *)eHandle;
    fprintf(fh, "KernelCh event %p tag = %s {\n", event, tag);
    fprintf(fh, "  parent            = %p\n", event->parent);
    fprintf(fh, "  channel           = %d\n", event->channelId);
    fprintf(fh, "}\n");                                    // 补充缺失的结束括号

  } else if (type == ncclProfileNetPlugin) {
    /* 调试网络插件事件信息 */
    struct netPlugin* event = (struct netPlugin *)eHandle;
    fprintf(fh, "NetPlugin event %p tag = %s {\n", event, tag);
    fprintf(fh, "  pluginType        = %d\n", event->pluginType);
    fprintf(fh, "  pluginVer         = %d\n", event->pluginVer);
    fprintf(fh, "  pluginEvent       = %d\n", event->pluginEvent);
    fprintf(fh, "  startTs           = %f\n", event->startTs);
    fprintf(fh, "  stopTs            = %f\n", event->stopTs);
    fprintf(fh, "}\n");
  }

  fclose(fh);                                               // 关闭调试文件
#endif
}

/**
 * @brief 递归打印事件及其子事件的 JSON 记录
 *
 * 这是主要的事件打印函数，负责将 NCCL profiler 收集的事件数据递归地
 * 格式化为 Chrome 跟踪格式的 JSON 输出。支持完整的事件层次结构，
 * 自动处理不同类型事件的嵌套关系。
 *
 * @param fh 文件句柄，指向要输出 JSON 数据的文件流
 * @param handle 事件句柄，指向要打印的事件对象
 *
 * @note 使用递归调用处理事件的层次结构
 * @note 输出的 JSON 格式兼容 Chrome 浏览器的 chrome://tracing 工具
 * @note 每种事件类型都有对应的开始和结束记录
 * @note 自动处理空指针和无效事件类型
 */
void printEvent(FILE* fh, void* handle) {
  if (handle == NULL || fh == NULL) return;                // 安全检查：验证输入参数
  uint8_t type = *(uint8_t *)handle;                        // 获取事件类型

  if (type == ncclProfileGroup) {
    /* 处理组事件：打印组及其包含的所有任务事件 */
    struct group* g = (struct group *)handle;
    printGroupEventHeader(fh, g);                           // 输出组事件开始记录

    // 遍历组中的所有任务事件（集合操作或点对点操作）
    struct taskEventBase* base = taskEventQueueHead(g);
    while (base) {
      struct taskEventBase* next = base->next;              // 保存下一个事件指针
      printEvent(fh, base);                                 // 递归打印任务事件
      base = next;
    }

    printGroupEventTrailer(fh, g);                          // 输出组事件结束记录

  } else if (type == ncclProfileColl) {
    /* 处理集合操作事件：打印集合操作及其相关的内核和代理事件 */
    struct collective* c = (struct collective *)handle;
    printCollEventHeader(fh, c);                            // 输出集合操作开始记录

    // 遍历所有通道
    for (int i = 0; i < MAX_CHANNELS; i++) {
      printKernelChEventHeader(fh, &c->kernel[i]);          // 输出内核通道事件开始记录

      // 遍历该通道的所有代理操作
      for (int j = 0; j < c->nProxyOps[i]; j++) {
        printEvent(fh, &c->op[i][j]);                       // 递归打印代理操作事件
      }

      printKernelChEventTrailer(fh, &c->kernel[i]);         // 输出内核通道事件结束记录
    }

    printCollEventTrailer(fh, c);                           // 输出集合操作结束记录

  } else if (type == ncclProfileP2p) {
    /* 处理点对点操作事件：打印点对点操作及其相关的内核和代理事件 */
    struct p2p* p = (struct p2p *)handle;
    printP2pEventHeader(fh, p);                             // 输出点对点操作开始记录

    // 遍历所有通道
    for (int i = 0; i < MAX_CHANNELS; i++) {
      printKernelChEventHeader(fh, &p->kernel[i]);          // 输出内核通道事件开始记录
      printEvent(fh, &p->op[i]);                            // 递归打印代理操作事件
      printKernelChEventTrailer(fh, &p->kernel[i]);         // 输出内核通道事件结束记录
    }

    printP2pEventTrailer(fh, p);                            // 输出点对点操作结束记录

  } else if (type == ncclProfileProxyOp) {
    /* 处理代理操作事件：打印代理操作及其包含的所有代理步骤 */
    struct proxyOp* p = (struct proxyOp *)handle;
    printProxyOpEventHeader(fh, p);                         // 输出代理操作开始记录

    // 遍历所有代理步骤
    for (int i = 0; i < MAX_STEPS; i++) {
      printEvent(fh, &p->step[i]);                          // 递归打印代理步骤事件
    }

    printProxyOpEventTrailer(fh, p);                        // 输出代理操作结束记录

  } else if (type == ncclProfileProxyStep) {
    /* 处理代理步骤事件：打印代理步骤及其相关的网络插件事件 */
    struct proxyStep* p = (struct proxyStep *)handle;
    printProxyStepEventHeader(fh, p);                       // 输出代理步骤开始记录

    // 遍历所有网络插件事件
    for (int q = 0; q < p->nNetEvents; q++) {
      printNetPluginEvent(fh, &p->net[q]);                  // 打印网络插件事件
    }

    printProxyStepEventTrailer(fh, p);                      // 输出代理步骤结束记录

  } else if (type == ncclProfileProxyCtrl) {
    /* 处理代理控制事件：打印代理控制状态变化 */
    struct proxyCtrl* p = (struct proxyCtrl *)handle;
    printProxyCtrlEvent(fh, p);                             // 输出代理控制事件记录
  }

  return;
}
