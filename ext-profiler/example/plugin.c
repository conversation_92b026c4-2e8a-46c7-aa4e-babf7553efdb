/*************************************************************************
 * Copyright (c) 2024, NVIDIA CORPORATION. All rights reserved.
 *
 * See LICENSE.txt for license information
 ************************************************************************/

/**
 * @file plugin.c
 * @brief NCCL Profiler 示例插件主实现文件
 *
 * 本文件实现了完整的 NCCL Profiler 插件功能，包括插件的初始化、
 * 事件处理、状态记录和资源管理。提供了符合 NCCL Profiler API v4
 * 规范的完整实现，支持多种事件类型的跟踪和分析。
 *
 * 主要功能：
 * - 实现 NCCL Profiler API v4 的所有接口函数
 * - 提供高精度时间测量和校准功能
 * - 管理多种类型事件的内存池
 * - 支持多线程安全的事件处理
 * - 实现事件层次结构和生命周期管理
 * - 支持 PXN (Parallel eXecution Network) 事件处理
 * - 提供性能数据的 JSON 格式输出
 */

#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <linux/limits.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/syscall.h>
#include <unistd.h>
#include <x86intrin.h>
#include "event.h"
#include "print_event.h"

/* 编译器属性定义 */
#define __hidden __attribute__ ((visibility("hidden")))  // 隐藏符号，仅内部使用

/* 全局状态变量 */
static int initialized;             // 初始化计数器：跟踪 profiler 的初始化状态
static double startTime;            // profiler 启动时间：用于计算相对时间戳

/* 默认配置常量 */
static const int defaultEActivationMask = ncclProfileColl | ncclProfileP2p;  // 默认事件激活掩码：集合操作和点对点操作
static const int defaultGroupPoolSize = 16;        // 默认组事件池大小
static const int defaultCollPoolSize = 16;         // 默认集合操作事件池大小
static const int defaultP2pPoolSize = 1024;        // 默认点对点操作事件池大小
static const int defaultProxyCtrlPoolSize = 16;    // 默认代理控制事件池大小
static const int defaultDetachPoolSize = 128;      // 默认分离事件池大小（用于 PXN）

/* 运行时配置变量 */
static int groupPoolSize;           // 组事件池大小：运行时确定的组事件池大小
static int collPoolSize;            // 集合操作事件池大小：运行时确定的集合操作事件池大小
static int p2pPoolSize;             // 点对点操作事件池大小：运行时确定的点对点操作事件池大小
static int proxyCtrlPoolSize;       // 代理控制事件池大小：运行时确定的代理控制事件池大小
static int detachPoolSize;          // 分离事件池大小：用于 PXN 的分离事件池大小
static int detachPoolBase;          // 分离事件池基址：分离事件池的基础索引
static int detachPoolIndex;         // 分离事件池索引：分离事件池的当前索引
static int detachPoolDone;          // 分离事件池完成计数：已完成的分离事件数量
static struct proxyOp* detachPool;  // 分离事件池指针：指向 PXN 分离事件池的内存

/* 日志记录相关 */
ncclDebugLogger_t logFn;            // 日志函数指针：NCCL 提供的日志记录函数
#define INFO(FLAGS, ...) logFn(NCCL_LOG_INFO, (FLAGS), __func__, __LINE__, __VA_ARGS__)  // 信息日志宏

/* 时间测量相关 */
static double freq = -1;            // CPU 频率：用于将 CPU 周期转换为时间

/**
 * @brief 校准 CPU 频率用于高精度时间测量
 *
 * 通过测量一定数量的 CPU 周期和对应的实际时间来计算 CPU 频率。
 * 使用 RDTSC 指令获取 CPU 时间戳计数器，结合系统时间来校准频率。
 * 这种方法提供了微秒级的时间测量精度。
 *
 * @note 此函数使用 x86 特定的 RDTSC 指令，仅适用于 x86/x64 架构
 * @note 校准过程中会执行大量 RDTSC 指令以提高测量精度
 */
__hidden void calibrate() {
  struct timeval tv;
  gettimeofday(&tv, NULL);                          // 获取校准开始时的系统时间
  uint64_t timeCycles = __rdtsc();                  // 获取校准开始时的 CPU 周期数
  double time = - tv.tv_sec*1e6 - tv.tv_usec;       // 计算负的开始时间（微秒）
  uint64_t total = 0ULL;                            // 累计变量，防止编译器优化
  for (int i = 0; i < 10000; i++) total += __rdtsc(); // 执行大量 RDTSC 指令
  gettimeofday(&tv, NULL);                          // 获取校准结束时的系统时间
  timeCycles = __rdtsc() - timeCycles;              // 计算经过的 CPU 周期数
  time += tv.tv_sec*1e6 + tv.tv_usec;               // 计算经过的实际时间（微秒）
  freq = timeCycles / time;                         // 计算 CPU 频率（周期/微秒）
}

/**
 * @brief 获取高精度时间戳
 *
 * 使用校准后的 CPU 频率将 RDTSC 计数器值转换为微秒时间戳。
 * 提供比系统调用更高精度和更低开销的时间测量。
 *
 * @return 当前时间戳（微秒）
 *
 * @note 返回的时间是相对于 calibrate() 调用时的相对时间
 * @note 精度取决于 CPU 频率的稳定性和校准精度
 */
__hidden double gettime(void) {
  return __rdtsc() / freq;                          // 将 CPU 周期转换为微秒时间
}

/* 线程同步和进程标识 */
static pthread_mutex_t lock = PTHREAD_MUTEX_INITIALIZER;  // 互斥锁：保护全局初始化过程的线程安全
static pid_t pid;                                         // 进程 ID：用于识别 PXN 事件的来源进程
static int* eActivationMaskPtr;                           // 事件激活掩码指针：指向当前激活的事件掩码

/**
 * @brief 初始化 profiler 插件
 *
 * 这是 NCCL Profiler API 的核心初始化函数，负责设置 profiler 的全局状态、
 * 分配内存池、配置环境参数等。支持多线程安全的初始化过程。
 *
 * @param context 输出参数，返回 profiler 上下文指针
 * @param eActivationMask 输入/输出参数，事件激活掩码
 * @param commName 通信器名称，用户指定的通信器标识
 * @param commHash 通信器哈希值，通信器的唯一标识符
 * @param nNodes 节点数量，通信器中的节点总数
 * @param nranks 排名数量，通信器中的排名总数
 * @param rank 当前排名，当前进程在通信器中的排名
 * @param logfn 日志函数，NCCL 提供的日志记录函数
 *
 * @return ncclSuccess 表示成功，ncclSystemError 表示系统错误
 *
 * @note 第一个调用此函数的线程负责全局初始化
 * @note 支持通过环境变量配置各种池大小和事件掩码
 * @note 使用原子操作确保多线程安全
 */
__hidden ncclResult_t exampleProfilerInit(void** context, int* eActivationMask, const char* commName, uint64_t commHash, int nNodes, int nranks, int rank, ncclDebugLogger_t logfn) {
  pthread_mutex_lock(&lock);                               // 获取互斥锁，保护初始化过程
  if (__atomic_fetch_add(&initialized, 1, __ATOMIC_RELAXED) == 0) {
    // 第一个线程负责全局初始化：事件掩码、环境变量和分离池
    const char* str;

    /* 配置事件激活掩码 */
    str = getenv("NCCL_PROFILE_EVENT_MASK");
    __atomic_store_n(eActivationMask, str ? atoi(str) : 0, __ATOMIC_RELAXED);

    /* 配置各种事件池大小 */
    str = getenv("NCCL_PROFILE_GROUP_POOL_SIZE");
    groupPoolSize = str ? atoi(str) : defaultGroupPoolSize;

    str = getenv("NCCL_PROFILE_COLL_POOL_SIZE");
    collPoolSize = str ? atoi(str) : defaultCollPoolSize;

    str = getenv("NCCL_PROFILE_P2P_POOL_SIZE");
    p2pPoolSize = str ? atoi(str) : defaultP2pPoolSize;

    str = getenv("NCCL_PROFILE_PROXY_CTRL_POOL_SIZE");
    proxyCtrlPoolSize = str ? atoi(str) : defaultProxyCtrlPoolSize;

    str = getenv("NCCL_PROFILE_PROXY_DETACH_POOL_SIZE");
    detachPoolSize = str ? atoi(str) : defaultDetachPoolSize;

    /* 分配 PXN 分离事件池 */
    // 分离池用于存储 PXN proxyOp 事件，在线程间共享
    detachPool = (struct proxyOp *)calloc(detachPoolSize, sizeof(*detachPool));
    if (detachPool == NULL) {
      pthread_mutex_unlock(&lock);
      return ncclSystemError;
    }

    /* 记录初始化进程的 PID */
    // 首次初始化 profiler 的进程 PID
    // 这个 PID 会与 proxyOp 事件的 PID 进行比较，
    // 以确定它们是否在当前进程地址空间中有父事件
    pid = getpid();

    /* 校准时间测量并启动计时器 */
    calibrate();                                            // 校准 CPU 频率
    startTime = gettime();                                  // 记录 profiler 启动时间
  }
  pthread_mutex_unlock(&lock);                              // 释放互斥锁

  /* 全局存储激活掩码指针 */
  eActivationMaskPtr = eActivationMask;

  /* 为专用 profiler 上下文预分配事件对象池内存 */
  struct context* ctx = (struct context *)calloc(1, sizeof(*ctx));
  if (ctx == NULL) return ncclSystemError;

  /* 初始化上下文基本信息 */
  ctx->commName = commName;
  ctx->commHash = commHash;
  ctx->nranks = nranks;
  ctx->rank = rank;
  logFn = logfn;
  INFO(NCCL_INIT, "PROFILER/Plugin: init commName: %s commHash: %lu nranks: %d rank: %d",
       commName ? commName : "", commHash, nranks, rank);

  /* 分配各种事件类型的内存池 */

  /* 分配组事件池 */
  ctx->groupPool = (struct group *)calloc(groupPoolSize, sizeof(*ctx->groupPool));
  if (ctx->groupPool == NULL) goto fail;

  /* 分配集合操作事件池 */
  ctx->collPool = (struct collective *)calloc(collPoolSize, sizeof(*ctx->collPool));
  if (ctx->collPool == NULL) goto fail;

  /* 分配点对点操作事件池 */
  ctx->p2pPool = (struct p2p *)calloc(p2pPoolSize, sizeof(*ctx->p2pPool));
  if (ctx->p2pPool == NULL) goto fail;

  /* 分配代理控制事件池 */
  ctx->proxyCtrlPool = (struct proxyCtrl *)calloc(proxyCtrlPoolSize, sizeof(*ctx->proxyCtrlPool));
  if (ctx->proxyCtrlPool == NULL) goto fail;

  /* 调试信息：打印事件池大小（已注释） */
  // 以下代码可用于调试，显示各个事件池占用的内存大小
  //fprintf(stdout, "Profiler: Group pool size (bytes): %lu\n", sizeof(struct group)*groupPoolSize);
  //fprintf(stdout, "Profiler: Coll  pool size (bytes): %lu\n", sizeof(struct collective)*collPoolSize);
  //fprintf(stdout, "Profiler: P2p   pool size (bytes): %lu\n", sizeof(struct p2p)*p2pPoolSize);
  //fprintf(stdout, "Profiler: Proxy pool size (bytes): %lu\n", sizeof(struct proxyCtrl)*proxyCtrlPoolSize);
  //fprintf(stdout, "Profiler: PXN   pool size (bytes): %lu\n", sizeof(struct proxyOp)*detachPoolSize);

  /* 成功初始化，返回上下文 */
  *context = ctx;
  return ncclSuccess;

fail:
  /* 错误处理：清理已分配的资源 */
  // 按照分配的逆序释放内存，避免内存泄漏
  if (ctx->proxyCtrlPool) free(ctx->proxyCtrlPool);
  if (ctx->p2pPool) free(ctx->p2pPool);
  if (ctx->collPool) free(ctx->collPool);
  if (ctx->groupPool) free(ctx->groupPool);
  free(ctx);
  if (detachPool) free(detachPool);
  return ncclSystemError;
}

/**
 * @brief 完成并清理 profiler 插件
 *
 * 这是 NCCL Profiler API 的清理函数，负责输出收集的性能数据、
 * 释放分配的内存资源、清理全局状态等。支持将性能数据导出为 JSON 格式。
 *
 * @param context profiler 上下文指针，由 init 函数创建
 *
 * @return ncclSuccess 表示成功完成清理
 *
 * @note 如果设置了 NCCL_PROFILE_DUMP_FILE 环境变量，会将性能数据输出到文件
 * @note 最后一个调用此函数的线程负责清理共享的分离池
 * @note 输出文件名格式为：{dump_file}_{commHash}_{rank}.json
 */
__hidden ncclResult_t exampleProfilerFinalize(void* context) {
  FILE* fh = NULL;                                          // 输出文件句柄
  char filename[PATH_MAX] = { 0 };                          // 输出文件名缓冲区
  struct context* ctx = (struct context *)context;

  /* 检查是否需要输出性能数据到文件 */
  const char* dump = getenv("NCCL_PROFILE_DUMP_FILE");
  if (dump) {
    sprintf(filename, "%s_%lu_%d.json", dump, ctx->commHash, ctx->rank);
    fh = fopen(filename, "w");
    if (fh) fprintf(fh, "[\n");                             // JSON 数组开始
  }

  INFO(NCCL_INIT, "PROFILER/Plugin: finalize commName: %s commHash: %lu nranks: %d rank: %d",
       ctx->commName ? ctx->commName : "", ctx->commHash, ctx->nranks, ctx->rank);

  /* 输出最近的 N 个组事件 */
  // 计算要输出的组事件范围（环形缓冲区）
  int start = (ctx->groupPoolIndex - groupPoolSize >= 0) ? ctx->groupPoolIndex - groupPoolSize : 0;
  int end = ctx->groupPoolIndex;
  for (int i = start; i < end; i++) {
    printEvent(fh, &ctx->groupPool[i%groupPoolSize]);
  }

  /* 输出最近的 N 个代理控制事件 */
  // 计算要输出的代理控制事件范围（环形缓冲区）
  start = (ctx->proxyCtrlPoolIndex - proxyCtrlPoolSize >= 0) ? ctx->proxyCtrlPoolIndex - proxyCtrlPoolSize : 0;
  end = ctx->proxyCtrlPoolIndex;
  for (int i = start; i < end; i++) {
    printEvent(fh, &ctx->proxyCtrlPool[i%proxyCtrlPoolSize]);
  }

  /* 释放当前上下文的内存池 */
  free(ctx->groupPool);
  free(ctx->collPool);
  free(ctx->p2pPool);
  free(ctx->proxyCtrlPool);
  free(ctx);

  /* 最后一个线程清理共享的分离池 */
  if (__atomic_sub_fetch(&initialized, 1, __ATOMIC_RELAXED) == 0) {
    // 当初始化计数器减为 0 时，表示这是最后一个调用 finalize 的线程
    // 负责清理全局共享的 PXN 分离事件池
    start = (detachPoolIndex - detachPoolSize >= 0) ? detachPoolIndex - detachPoolSize : 0;
    end = detachPoolIndex;
    for (int i = start; i < end; i++) {
      printEvent(fh, &detachPool[i%detachPoolSize]);
    }
    free(detachPool);                                       // 释放分离池内存
  }

  /* 完成 JSON 输出并关闭文件 */
  if (fh) fprintf(fh, "{}]\n");                             // JSON 数组结束
  if (fh) fclose(fh);

  return ncclSuccess;
}

/* 前向声明 */
__hidden void updateEvent(void* handle);

/**
 * @brief 启动新的 profiler 事件
 *
 * 这是 NCCL Profiler API 的核心函数，负责根据事件描述符创建相应类型的事件。
 * 支持多种事件类型，包括组事件、集合操作、点对点操作、代理操作等。
 * 使用内存池管理避免频繁的内存分配，提高性能。
 *
 * @param context profiler 上下文指针
 * @param eHandle 输出参数，返回事件句柄
 * @param eDescr 事件描述符，包含事件类型和相关元数据
 *
 * @return ncclSuccess 表示成功创建事件
 *
 * @note 如果内存池已满，会丢弃新事件而不是分配新内存
 * @note 使用原子操作确保多线程安全的池管理
 * @note 支持事件层次结构和引用计数管理
 */
__hidden ncclResult_t exampleProfilerStartEvent(void* context, void** eHandle, ncclProfilerEventDescr_t* eDescr) {
  *eHandle = NULL;                                          // 初始化事件句柄
  struct context* ctx = (struct context *)context;

  if (eDescr->type == ncclProfileGroup) {
    /* 处理组事件 */
    struct group* event;
    int groupId = __atomic_fetch_add(&ctx->groupPoolIndex, 1, __ATOMIC_RELAXED);

    if ((groupId - __atomic_load_n(&ctx->groupPoolBase, __ATOMIC_RELAXED)) < groupPoolSize) {
      // 如果有可用的组事件，获取一个
      event = &ctx->groupPool[groupId%groupPoolSize];

      /* 清理旧的任务事件队列 */
      while (!taskEventQueueEmpty(event)) {
        struct taskEventBase* base = taskEventQueueDequeue(event);
        if (base->type == ncclProfileColl) {
          struct collective* c = (struct collective *)base;
          // 重置事件的 proxyOp 和 proxyStep
          memset(c->nProxyOps, 0, sizeof(int)*MAX_CHANNELS);
          // 释放组中的集合事件并将其返回到集合池
          __atomic_fetch_add(&ctx->collPoolBase, 1, __ATOMIC_RELAXED);
        } else if (base->type == ncclProfileP2p) {
          struct p2p* p = (struct p2p *)base;
          // 重置事件的 proxyOp 和 proxyStep
          memset(&p->op, 0, sizeof(struct proxyOp)*MAX_CHANNELS);
          // 释放组中的点对点事件并将其返回到点对点池
          __atomic_fetch_add(&ctx->p2pPoolBase, 1, __ATOMIC_RELAXED);
        }
      }
    } else {
      // 否则丢弃此事件（池已满）
      __atomic_fetch_sub(&ctx->groupPoolIndex, 1, __ATOMIC_RELAXED);
      return ncclSuccess;
    }

    /* 初始化组事件 */
    event->type = ncclProfileGroup;
    event->ctx = ctx;
    event->groupId = groupId;
    event->startTs = gettime() - startTime;
    *eHandle = event;
    debugEvent(event, "GroupStart");
  } else if (eDescr->type == ncclProfileColl) {
    /* 处理集合操作事件 */
    // 如果事件用完，父对象可能为 null
    struct group* parent = (struct group *)eDescr->parentObj;
    if (parent == NULL) return ncclSuccess;

    struct collective* event;
    int collId = __atomic_fetch_add(&ctx->collPoolIndex, 1, __ATOMIC_RELAXED);

    if ((collId - __atomic_load_n(&ctx->collPoolBase, __ATOMIC_RELAXED)) < collPoolSize) {
      // 如果有可用的集合事件，获取一个
      event = &ctx->collPool[collId%collPoolSize];
    } else {
      // 否则丢弃此事件（池已满）
      __atomic_fetch_sub(&ctx->collPoolIndex, 1, __ATOMIC_RELAXED);
      return ncclSuccess;
    }

    /* 初始化集合操作事件的基础信息 */
    event->base.type = ncclProfileColl;
    event->base.rank = eDescr->rank;
    event->base.func = eDescr->coll.func;
    event->base.startTs = gettime() - startTime;
    event->base.parent = parent;

    /* 初始化集合操作特有的信息 */
    event->seqNumber = eDescr->coll.seqNumber;              // 序列号
    event->sendBuff = eDescr->coll.sendBuff;                // 发送缓冲区
    event->recvBuff = eDescr->coll.recvBuff;                // 接收缓冲区
    event->count = eDescr->coll.count;                      // 数据计数
    event->root = eDescr->coll.root;                        // 根节点排名
    event->datatype = eDescr->coll.datatype;                // 数据类型
    event->nChannels = eDescr->coll.nChannels;              // 通道数
    event->nWarps = eDescr->coll.nWarps;                    // GPU warp 数
    event->algo = eDescr->coll.algo;                        // 算法
    event->proto = eDescr->coll.proto;                      // 协议

    *eHandle = event;
    taskEventQueueEnqueue(parent, (struct taskEventBase *)event);
    // 增加组的引用计数，使事件保持打开状态
    __atomic_fetch_add(&parent->refCount, 1, __ATOMIC_RELAXED);
    debugEvent(event, "CollStart");
  } else if (eDescr->type == ncclProfileP2p) {
    /* 处理点对点操作事件 */
    // 如果事件用完，父对象可能为 null
    struct group* parent = (struct group *)eDescr->parentObj;
    if (parent == NULL) return ncclSuccess;

    struct p2p* event;
    int p2pId = __atomic_fetch_add(&ctx->p2pPoolIndex, 1, __ATOMIC_RELAXED);

    if ((p2pId - __atomic_load_n(&ctx->p2pPoolBase, __ATOMIC_RELAXED)) < p2pPoolSize) {
      // 如果有可用的点对点事件，获取一个
      event = &ctx->p2pPool[p2pId%p2pPoolSize];
    } else {
      // 否则丢弃此事件（池已满）
      __atomic_fetch_sub(&ctx->p2pPoolIndex, 1, __ATOMIC_RELAXED);
      return ncclSuccess;
    }

    /* 初始化点对点操作事件的基础信息 */
    event->base.type = ncclProfileP2p;
    event->base.rank = eDescr->rank;
    event->base.func = eDescr->p2p.func;
    event->base.next = parent->eventHead;
    event->base.startTs = gettime() - startTime;
    event->base.parent = parent;

    /* 初始化点对点操作特有的信息 */
    event->buff = eDescr->p2p.buff;                         // 数据缓冲区
    event->count = eDescr->p2p.count;                       // 数据计数
    event->datatype = eDescr->p2p.datatype;                 // 数据类型
    event->peer = eDescr->p2p.peer;                         // 对等方排名
    event->nChannels = eDescr->p2p.nChannels;               // 通道数

    *eHandle = event;
    // 增加组的引用计数，使事件保持打开状态
    taskEventQueueEnqueue(parent, (struct taskEventBase *)event);
    __atomic_fetch_add(&parent->refCount, 1, __ATOMIC_RELAXED);
    debugEvent(event, "P2pStart");
  } else if (eDescr->type == ncclProfileProxyCtrl) {
    /* 处理代理控制事件 */
    int proxyCtrlId = __atomic_fetch_add(&ctx->proxyCtrlPoolIndex, 1, __ATOMIC_RELAXED);
    struct proxyCtrl* event = &ctx->proxyCtrlPool[proxyCtrlId%proxyCtrlPoolSize];

    /* 初始化代理控制事件 */
    event->type = ncclProfileProxyCtrl;
    event->ctx = ctx;
    event->startTs = gettime() - startTime;
    *eHandle = event;
    debugEvent(event, "ProxyCtrlStart");
  } else if (eDescr->type == ncclProfileProxyOp) {
    /* 处理代理操作事件 */
    // 如果事件用完，eventBase 可能为 null
    struct taskEventBase* eventBase = (struct taskEventBase *)eDescr->parentObj;
    if (eventBase == NULL) return ncclSuccess;

    if (eDescr->proxyOp.pid != pid) {
      /* 处理 PXN 捕获的代理操作事件 */
      // PXN (Parallel eXecution Network) 事件来自不同的进程
      struct proxyOp* event;
      int detachId = __atomic_fetch_add(&detachPoolIndex, 1, __ATOMIC_RELAXED);

      if ((detachId - detachPoolBase) < detachPoolSize) {
        // 如果有可用的分离代理操作事件，获取一个
        event = &detachPool[detachId%detachPoolSize];
      } else {
        // 否则丢弃此事件（分离池已满）
        __atomic_fetch_sub(&detachPoolIndex, 1, __ATOMIC_RELAXED);
        return ncclSuccess;
      }

      /* 初始化 PXN 代理操作事件 */
      event->type = ncclProfileProxyOp;
      event->channelId = eDescr->proxyOp.channelId;           // 通道 ID
      event->pid = eDescr->proxyOp.pid;                       // 源进程 ID
      event->rank = eDescr->rank;                             // 排名
      event->peer = eDescr->proxyOp.peer;                     // 对等方排名
      event->nSteps = eDescr->proxyOp.nSteps;                 // 步骤数
      event->chunkSize = eDescr->proxyOp.chunkSize;           // 块大小
      event->isSend = eDescr->proxyOp.isSend;                 // 发送/接收标志
      event->startTs = gettime() - startTime;                 // 开始时间戳
      event->parent = NULL;                                   // PXN 事件没有本地父事件
      event->stepCount = 0;                                   // 初始化步骤计数
      *eHandle = event;
      debugEvent(event, "PxnProxyOpStart");
      return ncclSuccess;
    }

    if (eventBase->type == ncclProfileColl) {
      /* 处理集合操作的代理操作事件 */
      struct collective* parent = (struct collective *)eDescr->parentObj;
      int channelId = eDescr->proxyOp.channelId;
      // 在指定通道的代理操作数组中分配新的代理操作
      struct proxyOp* event = &parent->op[channelId][parent->nProxyOps[channelId]++];

      /* 初始化集合操作的代理操作事件 */
      event->type = ncclProfileProxyOp;
      event->channelId = channelId;                           // 通道 ID
      event->pid = eDescr->proxyOp.pid;                       // 进程 ID
      event->rank = eDescr->rank;                             // 排名
      event->peer = eDescr->proxyOp.peer;                     // 对等方排名
      event->nSteps = eDescr->proxyOp.nSteps;                 // 网络传输步骤数
      event->chunkSize = eDescr->proxyOp.chunkSize;           // 数据块大小
      event->isSend = eDescr->proxyOp.isSend;                 // 发送/接收操作标志
      event->parent = eventBase;                              // 指向父集合操作事件
      event->startTs = gettime() - startTime;                 // 记录开始时间戳
      event->stepCount = 0;                                   // 初始化步骤计数器
      *eHandle = event;
      // 增加父集合操作的引用计数
      __atomic_fetch_add(&parent->base.refCount, 1, __ATOMIC_RELAXED);
      debugEvent(event, "ProxyOpStart");

    } else { // ncclProfileP2p
      /* 处理点对点操作的代理操作事件 */
      struct p2p* parent = (struct p2p *)eDescr->parentObj;
      int channelId = eDescr->proxyOp.channelId;
      // 点对点操作每个通道只有一个代理操作
      struct proxyOp* event = &parent->op[channelId];

      /* 初始化点对点操作的代理操作事件 */
      event->type = ncclProfileProxyOp;
      event->channelId = channelId;                           // 通道 ID
      event->pid = eDescr->proxyOp.pid;                       // 进程 ID
      event->rank = eDescr->rank;                             // 排名
      event->peer = eDescr->proxyOp.peer;                     // 对等方排名
      event->nSteps = eDescr->proxyOp.nSteps;                 // 网络传输步骤数
      event->chunkSize = eDescr->proxyOp.chunkSize;           // 数据块大小
      event->isSend = eDescr->proxyOp.isSend;                 // 发送/接收操作标志
      event->parent = eventBase;                              // 指向父点对点操作事件
      event->startTs = gettime() - startTime;                 // 记录开始时间戳
      event->stepCount = 0;                                   // 初始化步骤计数器
      *eHandle = event;
      // 增加父点对点操作的引用计数
      __atomic_fetch_add(&parent->base.refCount, 1, __ATOMIC_RELAXED);
      debugEvent(event, "ProxyOpStart");
    }
  } else if (eDescr->type == ncclProfileProxyStep) {
    /* 处理代理步骤事件 */
    // 如果事件用完，父对象可能为 null
    struct proxyOp* parent = (struct proxyOp *)eDescr->parentObj;
    if (parent == NULL) return ncclSuccess;

    /* 在父代理操作的步骤数组中分配新步骤 */
    int s = parent->stepCount++ % MAX_STEPS;                  // 使用环形缓冲区索引
    struct proxyStep* event = &parent->step[s];

    /* 初始化代理步骤事件 */
    event->type = ncclProfileProxyStep;
    event->state = 0;                                        // 初始状态
    event->step = eDescr->proxyStep.step;                    // 步骤编号
    event->parent = parent;                                  // 指向父代理操作
    event->isSend = parent->isSend;                          // 继承父操作的发送/接收标志
    event->startTs = gettime() - startTime;                  // 记录开始时间戳
    event->nNetEvents = 0;                                   // 初始化网络事件计数
    *eHandle = event;
    debugEvent(event, "ProxyStepStart");
  } else if (eDescr->type == ncclProfileKernelCh) {
    /* 处理内核通道事件 */
    struct taskEventBase* eventBase = (struct taskEventBase *)eDescr->parentObj;
    if (eventBase == NULL) return ncclSuccess;

    if (eventBase->type == ncclProfileColl) {
      /* 处理集合操作的内核通道事件 */
      struct collective* parent = (struct collective *)eDescr->parentObj;
      struct kernelCh* event = &parent->kernel[eDescr->kernelCh.channelId];

      /* 初始化集合操作的内核通道事件 */
      event->type = ncclProfileKernelCh;
      event->channelId = eDescr->kernelCh.channelId;          // 通道 ID
      event->startGpuClk = eDescr->kernelCh.pTimer;           // GPU 开始时钟计数
      event->parent = eventBase;                              // 指向父集合操作事件
      event->startTs = gettime() - startTime;                 // CPU 开始时间戳
      *eHandle = event;
      // 增加父集合操作的引用计数
      __atomic_fetch_add(&parent->base.refCount, 1, __ATOMIC_RELAXED);
      debugEvent(event, "KernelChStart");

    } else { // ncclProfileP2p
      /* 处理点对点操作的内核通道事件 */
      struct p2p* parent = (struct p2p *)eDescr->parentObj;
      struct kernelCh* event = &parent->kernel[eDescr->kernelCh.channelId];

      /* 初始化点对点操作的内核通道事件 */
      event->type = ncclProfileKernelCh;
      event->channelId = eDescr->kernelCh.channelId;          // 通道 ID
      event->startGpuClk = eDescr->kernelCh.pTimer;           // GPU 开始时钟计数
      event->parent = eventBase;                              // 指向父点对点操作事件
      event->startTs = gettime() - startTime;                 // CPU 开始时间戳
      *eHandle = event;
      // 增加父点对点操作的引用计数
      __atomic_fetch_add(&parent->base.refCount, 1, __ATOMIC_RELAXED);
      debugEvent(event, "KernelChStart");
    }
  } else if (eDescr->type == ncclProfileNetPlugin) {
    /* 处理网络插件事件 */
    struct proxyStep* parent = (struct proxyStep *)eDescr->parentObj;
    if (parent == NULL) return ncclSuccess;

    /* 解析插件 ID 以确定网络类型和版本 */
    int64_t pluginId = eDescr->netPlugin.id;
    int64_t type = pluginId & NCCL_PROFILER_NET_TYPE_MASK;    // 提取网络类型
    int64_t ver = pluginId & NCCL_PROFILER_NET_VER_MASK;      // 提取版本信息

    if (type == NCCL_PROFILER_NET_TYPE_IB) {
      /* 处理 InfiniBand 网络插件事件 */
      if (ver == 1) {
        ncclProfilerNetIbDescr_v1_t* descr = (ncclProfilerNetIbDescr_v1_t *)eDescr->netPlugin.data;
        // 在父代理步骤的网络事件数组中分配新事件
        struct netPlugin* event = parent->net + __atomic_fetch_add(&parent->nNetEvents, 1, __ATOMIC_RELAXED);

        /* 初始化 InfiniBand 网络插件事件 */
        event->type = ncclProfileNetPlugin;
        event->pluginType = type;                             // 网络插件类型
        event->pluginVer = ver;                               // 插件版本

        if (descr->type == ncclProfileQp) {
          /* 处理 InfiniBand QP (Queue Pair) 事件 */
          event->pluginEvent = ncclProfileQp;
          event->qp.device = descr->qp.device;               // IB 设备 ID
          event->qp.wr_id = descr->qp.wr_id;                 // 工作请求 ID
          event->qp.opcode = descr->qp.opcode;               // IBV 操作码
          event->qp.qpNum = descr->qp.qpNum;                 // QP 号码
          event->qp.length = descr->qp.length;               // 数据长度
        }
        event->startTs = gettime() - startTime;              // 记录开始时间戳
        event->parent = parent;                               // 设置父代理步骤
        *eHandle = event;
        debugEvent(event, "NetPluginStart");
      }

    } else if (type == NCCL_PROFILER_NET_TYPE_SOCK) {
      /* 处理 Socket 网络插件事件 */
      if (ver == 1) {
        ncclProfilerNetSockDescr_v1_t* descr = (ncclProfilerNetSockDescr_v1_t *)eDescr->netPlugin.data;
        // 在父代理步骤的网络事件数组中分配新事件
        struct netPlugin* event = parent->net + __atomic_fetch_add(&parent->nNetEvents, 1, __ATOMIC_RELAXED);

        /* 初始化 Socket 网络插件事件 */
        event->type = ncclProfileNetPlugin;
        event->pluginType = type;                             // 网络插件类型
        event->pluginVer = ver;                               // 插件版本

        if (descr->type == ncclProfileSocket) {
          /* 处理 Socket 事件 */
          event->pluginEvent = ncclProfileSocket;
          event->sock.fd = descr->sock.fd;                   // Socket 文件描述符
          event->sock.op = descr->sock.op;                   // Socket 操作类型
          event->sock.length = descr->sock.length;           // 数据长度
        }
        event->startTs = gettime() - startTime;              // 记录开始时间戳
        event->parent = parent;                               // 设置父代理步骤
        *eHandle = event;
        debugEvent(event, "NetPluginStart");
      }
    }
  }
  return ncclSuccess;
}

/**
 * @brief 更新事件状态并处理引用计数
 *
 * 内部函数，用于更新各种类型事件的状态，处理引用计数的递减，
 * 并在引用计数归零时完成事件的生命周期管理。支持事件层次结构
 * 的级联更新。
 *
 * @param handle 事件句柄，指向要更新的事件对象
 *
 * @note 此函数通过事件类型字段识别具体的事件类型
 * @note 使用原子操作确保引用计数的线程安全
 * @note 支持递归调用以处理事件层次结构
 */
void updateEvent(void* handle) {
  uint8_t type = *(uint8_t *)handle;                        // 获取事件类型

  if (type == ncclProfileGroup) {
    /* 处理组事件更新 */
    struct group* event = (struct group *)handle;
    if (__atomic_sub_fetch(&event->refCount, 1, __ATOMIC_RELAXED) == 0) {
      // 引用计数归零，完成组事件
      event->stopTs = gettime() - startTime;
      // 将组事件返回到池中
      __atomic_fetch_add(&event->ctx->groupPoolBase, 1, __ATOMIC_RELAXED);
    }
    debugEvent(event, "GroupStop");

  } else if (type == ncclProfileColl) {
    /* 处理集合操作事件更新 */
    struct collective* event = (struct collective *)handle;
    if (__atomic_sub_fetch(&event->base.refCount, 1, __ATOMIC_RELAXED) == 0) {
      // 引用计数归零，完成集合操作事件
      event->base.stopTs = gettime() - startTime;
      debugEvent(event, "CollStop");
      updateEvent(event->base.parent);                      // 递归更新父事件
      return;
    }
    debugEvent(event, "CollStop");

  } else if (type == ncclProfileP2p) {
    /* 处理点对点操作事件更新 */
    struct p2p* event = (struct p2p *)handle;
    if (__atomic_sub_fetch(&event->base.refCount, 1, __ATOMIC_RELAXED) == 0) {
      // 引用计数归零，完成点对点操作事件
      event->base.stopTs = gettime() - startTime;
      debugEvent(event, "P2pStop");
      updateEvent(event->base.parent);                      // 递归更新父事件
      return;
    }
    debugEvent(event, "P2pStop");
  } else if (type == ncclProfileProxyOp) {
    /* 处理代理操作事件更新 */
    struct proxyOp* event = (struct proxyOp *)handle;
    event->stopTs = gettime() - startTime;

    if (event->pid != pid) {
      // 仅适用于没有父集合操作/点对点操作的 proxyOp（即 PXN）
      int done = __atomic_add_fetch(&detachPoolDone, 1, __ATOMIC_RELAXED);
      if (done == detachPoolSize) {
        // 重置事件完成计数器
        __atomic_store_n(&detachPoolDone, 0, __ATOMIC_RELAXED);
        // 更新基础指针到池的顶部
        int index = __atomic_load_n(&detachPoolIndex, __ATOMIC_RELAXED);
        __atomic_store_n(&detachPoolBase, index, __ATOMIC_RELAXED);
      }
      debugEvent(event, "ProxyOpStop");
      return;
    }
    updateEvent(event->parent);                             // 更新父事件
    debugEvent(event, "ProxyOpStop");

  } else if (type == ncclProfileProxyStep) {
    /* 处理代理步骤事件更新 */
    struct proxyStep* event = (struct proxyStep *)handle;
    event->stopTs = gettime() - startTime;
    debugEvent(event, "ProxyStepStop");

  } else if (type == ncclProfileProxyCtrl) {
    /* 处理代理控制事件更新 */
    struct proxyCtrl* event = (struct proxyCtrl *)handle;
    event->stopTs = gettime() - startTime;
    debugEvent(event, "ProxyCtrlStop");

  } else if (type == ncclProfileKernelCh) {
    /* 处理内核通道事件更新 */
    struct kernelCh* event = (struct kernelCh *)handle;
    event->stopTs = gettime() - startTime;
    updateEvent(event->parent);                             // 更新父事件
    debugEvent(event, "KernelChStop");

  } else if (type == ncclProfileNetPlugin) {
    /* 处理网络插件事件更新 */
    struct netPlugin* event = (struct netPlugin *)handle;
    event->stopTs = gettime() - startTime;
    debugEvent(event, "NetPluginStop");
  }
}

/**
 * @brief 停止 profiler 事件
 *
 * 这是 NCCL Profiler API 的事件停止函数，负责标记事件的结束时间。
 * 对于不同类型的事件有不同的处理逻辑：高级事件（组、集合操作、点对点）
 * 仅记录停止时间但保持事件打开，低级事件则完全停止。
 *
 * @param eHandle 事件句柄，指向要停止的事件对象
 *
 * @return ncclSuccess 表示成功停止事件
 *
 * @note 对于组、集合操作和点对点事件，停止并不意味着完成，只是入队
 * @note 其他类型的事件会调用 updateEvent 进行完整的停止处理
 * @note 支持空句柄的安全处理
 */
__hidden ncclResult_t exampleProfilerStopEvent(void* eHandle) {
  // 如果事件用完，事件句柄可能为 null
  if (eHandle == NULL) return ncclSuccess;

  uint8_t type = *(uint8_t *)eHandle;

  if (type == ncclProfileGroup) {
    /* 处理组事件停止 */
    // 在 NCCL 核心中停止组事件并不意味着组已完成。
    // 它意味着组已提交/入队，所以我们需要保持事件打开
    struct group* event = (struct group *)eHandle;
    event->stopTs = gettime() - startTime;
    return ncclSuccess;

  } else if (type == ncclProfileColl) {
    /* 处理集合操作事件停止 */
    // 在 NCCL 核心中停止集合事件并不意味着集合操作已完成。
    // 它意味着集合操作已提交/入队，所以我们需要保持事件打开
    struct collective* event = (struct collective *)eHandle;
    event->base.stopTs = gettime() - startTime;
    return ncclSuccess;

  } else if (type == ncclProfileP2p) {
    /* 处理点对点操作事件停止 */
    // 在 NCCL 核心中停止点对点事件并不意味着点对点操作已完成。
    // 它意味着点对点操作已提交/入队，所以我们需要保持事件打开
    struct p2p* event = (struct p2p *)eHandle;
    event->base.stopTs = gettime() - startTime;
    return ncclSuccess;
  }

  /* 对于其他类型的事件，调用 updateEvent 进行完整停止 */
  updateEvent(eHandle);
  return ncclSuccess;
}

/**
 * @brief 记录事件状态转换
 *
 * 这是 NCCL Profiler API 的状态记录函数，负责记录事件在执行过程中的
 * 状态转换和相关属性更新。不同类型的事件支持不同的状态转换。
 *
 * @param eHandle 事件句柄，指向要更新状态的事件对象
 * @param eState 事件状态，新的状态值
 * @param eStateArgs 状态参数，包含状态转换相关的附加信息
 *
 * @return ncclSuccess 表示成功记录状态
 *
 * @note 主要用于代理操作、代理步骤、代理控制和内核通道事件
 * @note 记录详细的时间戳和传输大小等性能关键信息
 * @note 支持复杂的状态机转换逻辑
 */
__hidden ncclResult_t exampleProfilerRecordEventState(void* eHandle, ncclProfilerEventState_t eState, ncclProfilerEventStateArgs_t* eStateArgs) {
  // 如果事件用完，事件句柄可能为 null
  if (eHandle == NULL) return ncclSuccess;

  uint8_t type = *(uint8_t *)eHandle;

  if (type == ncclProfileProxyOp) {
    /* 处理代理操作事件状态记录 */
    struct proxyOp* event = (struct proxyOp *)eHandle;
    if (eState == ncclProfilerProxyOpInProgress_v4) {
      event->progrTs = gettime() - startTime;               // 记录进行中状态的时间戳
    }

  } else if (type == ncclProfileProxyStep) {
    /* 处理代理步骤事件状态记录 */
    struct proxyStep* event = (struct proxyStep *)eHandle;
    struct proxyOp* parent = event->parent;

    switch (eState) {
      case ncclProfilerProxyStepSendGPUWait:
        // 发送 GPU 等待状态
        event->timestamp[PROXY_STEP_SEND_GPU_WAIT] = gettime() - startTime;
        break;
      case ncclProfilerProxyStepSendPeerWait_v4:
        // 发送对等等待状态
        // 如果已经在 SendPeerWait 状态，不更新步骤事件
        if (event->state == ncclProfilerProxyStepSendPeerWait_v4) break;
        event->timestamp[PROXY_STEP_SEND_PEER_WAIT] = gettime() - startTime;
        event->state = ncclProfilerProxyStepSendPeerWait_v4;
        break;
      case ncclProfilerProxyStepSendWait:
        // 发送等待状态
        event->timestamp[PROXY_STEP_SEND_WAIT] = gettime() - startTime;
        parent->transSize += eStateArgs->proxyStep.transSize; // 累加传输大小
        break;
      case ncclProfilerProxyStepRecvWait:
        // 接收等待状态
        event->timestamp[PROXY_STEP_RECV_WAIT] = gettime() - startTime;
        break;
      case ncclProfilerProxyStepRecvFlushWait:
        // 接收刷新等待状态
        event->timestamp[PROXY_STEP_RECV_FLUSH_WAIT] = gettime() - startTime;
        parent->transSize += eStateArgs->proxyStep.transSize; // 累加传输大小
        break;
      case ncclProfilerProxyStepRecvGPUWait:
        // 接收 GPU 等待状态
        event->timestamp[PROXY_STEP_RECV_GPU_WAIT] = gettime() - startTime;
        break;
    }

  } else if (type == ncclProfileProxyCtrl) {
    /* 处理代理控制事件状态记录 */
    struct proxyCtrl* event = (struct proxyCtrl *)eHandle;
    if (eState == ncclProfilerProxyCtrlAppendEnd) {
      event->appended = eStateArgs->proxyCtrl.appendedProxyOps; // 记录追加的代理操作数
    }
    event->state = eState;                                  // 更新控制状态

  } else if (type == ncclProfileKernelCh) {
    /* 处理内核通道事件状态记录 */
    struct kernelCh* event = (struct kernelCh *)eHandle;
    if (eState == ncclProfilerKernelChStop) {
      event->stopGpuClk = eStateArgs->kernelCh.pTimer;      // 记录停止时的 GPU 时钟
    }
  }

  debugEvent(eHandle, "RecordEventState");
  return ncclSuccess;
}

/**
 * @brief NCCL Profiler v4 接口结构体
 *
 * 这是符合 NCCL Profiler API v4 规范的完整接口实现。
 * NCCL 核心通过查找此符号来加载和使用 profiler 插件。
 */
ncclProfiler_t ncclProfiler_v4 = {
  "Example-profiler",                                       // 插件名称
  exampleProfilerInit,                                      // 初始化函数
  exampleProfilerStartEvent,                                // 启动事件函数
  exampleProfilerStopEvent,                                 // 停止事件函数
  exampleProfilerRecordEventState,                          // 记录事件状态函数
  exampleProfilerFinalize,                                  // 完成清理函数
};

/**
 * @brief 启动示例 profiler 并设置事件激活掩码
 *
 * 外部控制接口，允许应用程序动态启动 profiler 并指定要跟踪的事件类型。
 * 只有在 profiler 已经初始化的情况下才会生效。
 *
 * @param eActivationMask 事件激活掩码，指定要激活的事件类型
 *
 * @return ncclSuccess 表示成功设置激活掩码
 *
 * @note 使用原子操作确保线程安全
 * @note 可以在运行时多次调用以动态调整事件跟踪
 */
int exampleProfilerStart(int eActivationMask) {
  if (__atomic_load_n(&initialized, __ATOMIC_RELAXED)) {
    __atomic_store_n(eActivationMaskPtr, eActivationMask, __ATOMIC_RELAXED);
  }
  return ncclSuccess;
}

/**
 * @brief 停止示例 profiler 的事件跟踪
 *
 * 外部控制接口，允许应用程序动态停止 profiler 的事件跟踪。
 * 相当于将事件激活掩码设置为 0。
 *
 * @return ncclSuccess 表示成功停止事件跟踪
 *
 * @note 使用原子操作确保线程安全
 * @note 不影响已收集的数据，只停止新事件的收集
 * @note 可以通过 exampleProfilerStart 重新启动
 */
int exampleProfilerStop(void) {
  if (__atomic_load_n(&initialized, __ATOMIC_RELAXED)) {
    __atomic_store_n(eActivationMaskPtr, 0, __ATOMIC_RELAXED);
  }
  return ncclSuccess;
}
