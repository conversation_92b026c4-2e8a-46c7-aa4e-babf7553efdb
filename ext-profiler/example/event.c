/*************************************************************************
 * Copyright (c) 2024, NVIDIA CORPORATION. All rights reserved.
 *
 * See LICENSE.txt for license information
 ************************************************************************/

/**
 * @file event.c
 * @brief NCCL Profiler 示例插件事件队列管理实现
 *
 * 本文件实现了任务事件队列的基本操作，包括队列的空检查、入队、
 * 获取队列头部和出队操作。这些函数用于管理组事件中的任务事件队列，
 * 支持 NCCL 操作的有序处理和生命周期管理。
 *
 * 主要功能：
 * - 提供任务事件队列的基本操作接口
 * - 支持 FIFO（先进先出）队列语义
 * - 维护队列的头尾指针，确保操作效率
 * - 处理空队列的边界情况
 */

#include <stdio.h>
#include "event.h"

/**
 * @brief 检查任务事件队列是否为空
 *
 * 通过检查队列头指针是否为 NULL 来判断队列是否为空。
 * 这是一个简单而高效的空队列检测方法。
 *
 * @param g 组事件指针，不能为 NULL
 * @return 1 表示队列为空，0 表示队列非空
 *
 * @note 调用者需要确保传入的组指针有效
 */
int taskEventQueueEmpty(struct group* g) {
  return g->eventHead == NULL;
}

/**
 * @brief 将任务事件加入队列尾部
 *
 * 实现 FIFO 队列的入队操作。新事件总是添加到队列的尾部，
 * 保持事件的处理顺序与加入顺序一致。
 *
 * @param g 组事件指针，不能为 NULL
 * @param event 要加入队列的任务事件指针，不能为 NULL
 *
 * @note 函数会自动处理空队列的情况，确保头尾指针的正确维护
 */
void taskEventQueueEnqueue(struct group* g, struct taskEventBase* event) {
  event->next = NULL;                    // 清空新事件的 next 指针
  if (g->eventHead) {                    // 如果队列非空
    g->eventTail->next = event;          // 将新事件链接到当前尾部
  } else {                               // 如果队列为空
    g->eventHead = event;                // 新事件成为队列头部
  }
  g->eventTail = event;                  // 更新队列尾部指针
}

/**
 * @brief 获取任务事件队列的头部事件
 *
 * 返回队列头部的事件，但不从队列中移除该事件。
 * 这允许调用者查看下一个要处理的事件而不改变队列状态。
 *
 * @param g 组事件指针，不能为 NULL
 * @return 队列头部的任务事件指针，队列为空时返回 NULL
 *
 * @note 返回的指针指向队列中的事件，调用者不应修改队列结构
 */
struct taskEventBase* taskEventQueueHead(struct group* g) {
  return g->eventHead;
}

/**
 * @brief 从任务事件队列中移除并返回头部事件
 *
 * 实现 FIFO 队列的出队操作。移除队列头部的事件并返回其指针，
 * 同时更新队列的头指针。如果队列变为空，还会清空尾指针。
 *
 * @param g 组事件指针，不能为 NULL
 * @return 被移除的任务事件指针，队列为空时返回 NULL
 *
 * @note 调用者负责管理返回的事件指针的生命周期
 * @warning 在空队列上调用此函数会导致未定义行为，建议先检查队列是否为空
 */
struct taskEventBase* taskEventQueueDequeue(struct group* g) {
  struct taskEventBase* tmp = g->eventHead;  // 保存当前头部事件
  if (tmp != NULL) {                         // 如果队列非空
    g->eventHead = g->eventHead->next;       // 更新头指针到下一个事件
    if (g->eventHead == NULL) {              // 如果队列变为空
      g->eventTail = NULL;                   // 清空尾指针
    }
  }
  return tmp;                                // 返回原头部事件
}
