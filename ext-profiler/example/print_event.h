/*************************************************************************
 * Copyright (c) 2024, NVIDIA CORPORATION. All rights reserved.
 *
 * See LICENSE.txt for license information
 ************************************************************************/

/**
 * @file print_event.h
 * @brief NCCL Profiler 示例插件事件打印功能头文件
 *
 * 本文件定义了 NCCL Profiler 插件中用于事件输出和调试的函数接口。
 * 主要功能包括将收集的性能事件数据格式化为 Chrome 跟踪格式的 JSON 输出，
 * 以及提供调试模式下的详细事件信息输出。
 *
 * 主要功能：
 * - 提供事件数据的 JSON 格式化输出接口
 * - 支持 Chrome 跟踪格式兼容的异步事件输出
 * - 提供调试模式下的详细事件信息输出
 * - 支持递归的事件层次结构打印
 */

#ifndef PRINT_EVENT_H_
#define PRINT_EVENT_H_

#include "nccl/common.h"

/* 外部声明：日志函数指针 */
extern ncclDebugLogger_t logFn;  // NCCL 提供的日志记录函数

/**
 * @brief 调试模式下输出事件详细信息
 *
 * 在调试模式下（定义了 DEBUG_EVENTS 宏时）输出事件的详细信息到调试文件。
 * 包括事件类型、引用计数、时间戳、父子关系等调试信息。
 *
 * @param eHandle 事件句柄，指向要调试输出的事件对象
 * @param tag 调试标签，用于标识调试输出的上下文（如 "Start", "Stop" 等）
 *
 * @note 只有在编译时定义了 DEBUG_EVENTS 宏才会实际输出调试信息
 * @note 调试信息输出到名为 "EventDebug-{pid}" 的文件中
 * @note 支持所有类型的 NCCL profiler 事件
 */
void debugEvent(void* eHandle, const char* tag);

/**
 * @brief 将事件数据格式化输出为 Chrome 跟踪格式的 JSON
 *
 * 将 NCCL profiler 收集的事件数据递归地格式化为符合 Chrome 跟踪格式的
 * JSON 输出。支持完整的事件层次结构，包括组事件、集合操作、点对点操作、
 * 代理操作、代理步骤、内核通道事件和网络插件事件。
 *
 * @param fh 文件句柄，指向要输出 JSON 数据的文件流
 * @param handle 事件句柄，指向要打印的事件对象
 *
 * @note 输出的 JSON 格式兼容 Chrome 浏览器的 chrome://tracing 工具
 * @note 使用异步事件格式（"ph": "b" 和 "ph": "e"）表示事件的开始和结束
 * @note 支持递归打印，自动处理事件的层次结构关系
 * @note 为不同类型的事件分配不同的类别（GROUP、COLL、P2P、PROXY、NET、GPU）
 */
void printEvent(FILE* fh, void* handle);

#endif
