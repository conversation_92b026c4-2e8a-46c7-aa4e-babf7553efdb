/*************************************************************************
 * Copyright (c) 2024, NVIDIA CORPORATION. All rights reserved.
 *
 * See LICENSE.txt for license information
 ************************************************************************/

/**
 * @file event.h
 * @brief NCCL Profiler 示例插件事件定义头文件
 *
 * 本文件定义了 NCCL Profiler 插件中使用的各种事件结构体和相关常量。
 * 包括网络插件事件、内核通道事件、代理步骤事件、代理操作事件、
 * 集合操作事件、点对点事件、组事件以及 profiler 上下文等核心数据结构。
 *
 * 主要功能：
 * - 定义 NCCL 性能分析中的各种事件类型
 * - 提供事件层次结构的数据结构支持
 * - 实现事件队列管理的基础设施
 * - 支持多种网络插件和内核事件的跟踪
 */

#ifndef EVENT_H_
#define EVENT_H_

#include <sys/types.h>
#include <stdint.h>
#include <unistd.h>
#include "profiler.h"

/* 系统限制常量定义 */
#define MAX_CHANNELS                     32    // 最大通道数：NCCL 支持的最大并行通道数量
#define MAX_STEPS                        16    // 最大步骤数：单个代理操作中的最大网络传输步骤数
#define MAX_OPS                          16    // 最大操作数：支持最多 64K 个排名的 PAT (Parallel All-to-All Transport)
#define MAX_EVENTS_PER_REQ               (8)   // 每个请求的最大事件数：单个网络请求可以关联的最大事件数量

/* 前向声明：避免循环依赖 */
struct proxyOp;      // 代理操作结构体前向声明
struct proxyStep;    // 代理步骤结构体前向声明

/**
 * @brief 网络插件事件结构体
 *
 * 用于跟踪网络插件生成的事件，支持多种网络类型（如 InfiniBand、Socket 等）。
 * 网络插件可以通过此结构体向 profiler 报告网络层面的详细操作信息。
 */
struct netPlugin {
  uint8_t type;           // 事件类型：标识具体的网络事件类型
  int pluginType;         // 插件类型：区分不同的网络插件（IB、Socket 等）
  int pluginVer;          // 插件版本：用于版本兼容性检查
  uint8_t pluginEvent;    // 插件事件：插件定义的具体事件标识

  union {
    /**
     * @brief InfiniBand QP (Queue Pair) 事件数据
     * 用于 InfiniBand 网络的队列对操作跟踪
     */
    struct {
      int device;         // 网络设备 ID：标识使用的 IB 设备
      int qpNum;          // QP 号码：InfiniBand 队列对编号
      int opcode;         // 操作码：IBV 操作类型（发送、接收等）
      uint64_t wr_id;     // 工作请求 ID：唯一标识工作请求
      size_t length;      // 数据长度：工作请求传输的数据大小
    } qp;

    /**
     * @brief Socket 网络事件数据
     * 用于基于 Socket 的网络操作跟踪
     */
    struct {
      int fd;             // 文件描述符：Socket 文件描述符
      int op;             // 操作类型：Socket 操作类型（读、写等）
      size_t length;      // 数据长度：Socket 操作的数据大小
    } sock;
  };

  double startTs;         // 开始时间戳：事件开始的时间（秒）
  double stopTs;          // 结束时间戳：事件结束的时间（秒）
  struct proxyStep* parent; // 父事件指针：指向包含此网络事件的代理步骤
};

/**
 * @brief 内核通道事件结构体
 *
 * 用于跟踪 GPU 内核在特定通道上的操作。内核通道事件提供了
 * GPU 端操作的时间信息，包括 CPU 时间戳和 GPU 时钟计数。
 */
struct kernelCh {
  uint8_t type;                   // 事件类型：标识为内核通道事件
  uint8_t channelId;              // 通道 ID：标识 GPU 内核使用的通道编号
  struct taskEventBase* parent;   // 父事件指针：指向包含此内核事件的任务事件（集合操作或点对点操作）
  double startTs;                 // 开始时间戳：事件开始的 CPU 时间（秒）
  double stopTs;                  // 结束时间戳：事件结束的 CPU 时间（秒）
  uint64_t startGpuClk;          // 开始 GPU 时钟：事件开始时的 GPU 时钟计数
  uint64_t stopGpuClk;           // 结束 GPU 时钟：事件结束时的 GPU 时钟计数
};

/* 代理步骤状态常量定义 */
/* 发送操作的状态定义 */
#define PROXY_STEP_SEND_GPU_WAIT 0      // 发送 GPU 等待：等待 GPU 提供发送数据
#define PROXY_STEP_SEND_PEER_WAIT 1     // 发送对等等待：等待对等方的接收准备信号
#define PROXY_STEP_SEND_WAIT 2          // 发送等待：等待网络发送操作完成

/* 接收操作的状态定义 */
#define PROXY_STEP_RECV_WAIT 0          // 接收等待：等待网络接收操作完成
#define PROXY_STEP_RECV_FLUSH_WAIT 1    // 接收刷新等待：等待接收数据刷新到 GPU 内存
#define PROXY_STEP_RECV_GPU_WAIT 2      // 接收 GPU 等待：等待 GPU 消费接收到的数据

#define PROXY_STEP_MAX_STATES 3         // 最大状态数：代理步骤支持的最大状态数量

/**
 * @brief 代理步骤事件结构体
 *
 * 代表代理进度线程中的单个网络传输步骤。每个代理操作可能包含多个步骤，
 * 每个步骤对应一次具体的网络数据传输。提供细粒度的网络操作性能分析。
 */
struct proxyStep {
  uint8_t type;                     // 事件类型：标识为网络传输事件
  int state;                        // 当前状态：步骤的执行状态（等待、进行中、完成等）
  int step;                         // 步骤编号：在给定通道中的网络传输 ID
  int isSend;                       // 操作方向：1 表示发送操作，0 表示接收操作
  double timestamp[PROXY_STEP_MAX_STATES]; // 状态时间戳数组：记录各个状态转换的时间点
  double startTs;                   // 开始时间戳：步骤开始执行的时间
  double stopTs;                    // 结束时间戳：步骤完成执行的时间
  struct proxyOp* parent;           // 父操作指针：指向包含此步骤的代理操作
  struct netPlugin net[MAX_EVENTS_PER_REQ]; // 网络事件数组：关联的网络插件事件
  int nNetEvents;                   // 网络事件数量：当前步骤关联的网络事件个数
};

/**
 * @brief 代理操作事件结构体
 *
 * 代表代理进度线程处理的完整网络操作。一个代理操作通常对应一个
 * NCCL 集合操作或点对点操作在特定通道上的网络传输任务。
 */
struct proxyOp {
  uint8_t type;                     // 事件类型：标识为代理操作事件
  uint8_t channelId;                // 通道 ID：此代理操作使用的通道编号
  pid_t pid;                        // 进程 ID：生成此操作的进程标识符
  int rank;                         // 排名：操作所属的 NCCL 排名
  int peer;                         // 对等排名：此代理操作的对等方排名
  int nSteps;                       // 步骤总数：此代理操作的网络传输步骤总数
  int chunkSize;                    // 块大小：此代理操作使用的数据块大小
  int isSend;                       // 操作方向：1 表示发送通道操作，0 表示接收通道操作
  size_t transSize;                 // 传输大小：此代理操作传输的数据总大小
  double startTs;                   // 开始时间戳：操作开始的时间
  double progrTs;                   // 进行中时间戳：操作转为进行中状态的时间
  double stopTs;                    // 结束时间戳：操作完成的时间
  int stepCount;                    // 步骤计数：此代理操作最后处理的网络操作编号
  struct proxyStep step[MAX_STEPS]; // 步骤数组：网络传输事件的数组
  struct taskEventBase* parent;     // 父事件指针：指向父事件（点对点或集合操作）
};

/* 前向声明：避免循环依赖 */
struct group;        // 组事件结构体前向声明
struct context;      // 上下文结构体前向声明

/**
 * @brief 代理控制事件结构体
 *
 * 用于跟踪代理进度线程的控制状态，包括线程的空闲、活动、睡眠、
 * 唤醒等状态转换，以及新代理操作的追加等控制操作。
 */
struct proxyCtrl {
  uint8_t type;                     // 事件类型：标识为代理控制事件
  struct context* ctx;              // profiler 上下文：指向 profiler 上下文对象
  double startTs;                   // 开始时间戳：控制事件开始的时间
  double stopTs;                    // 结束时间戳：控制事件结束的时间
  int state;                        // 控制状态：代理线程的当前控制状态
  int appended;                     // 追加计数：已追加的代理操作数量
};

/**
 * @brief 任务级事件基础结构体
 *
 * 所有任务级事件（集合操作、点对点操作）的基础结构。提供了
 * 事件的基本属性和层次结构管理功能。
 */
struct taskEventBase {
  uint8_t type;                     // 事件类型：集合操作或点对点操作
  int rank;                         // 排名：操作在 NCCL 通信器中的排名
  const char* func;                 // 函数名：NCCL 函数名称（如 "AllReduce"）
  int refCount;                     // 引用计数：此操作的引用数量，用于生命周期管理
  struct group* parent;             // 父组指针：指向包含此事件的组事件
  struct taskEventBase* next;       // 下一个事件：组中下一个顶级事件的指针
  double startTs;                   // 开始时间戳：事件开始的时间
  double stopTs;                    // 结束时间戳：事件结束的时间
};

/**
 * @brief 集合操作事件结构体
 *
 * 代表 NCCL 集合操作（如 AllReduce、AllGather、Broadcast 等）的完整信息。
 * 包含操作的元数据、使用的算法和协议、以及相关的代理操作和内核事件。
 */
struct collective {
  struct taskEventBase base;        // 基础结构：继承任务事件的基本属性
  uint64_t seqNumber;               // 序列号：此集合操作在通信器中的序列号
  void const* sendBuff;             // 发送缓冲区：发送数据的内存地址
  void* recvBuff;                   // 接收缓冲区：接收数据的内存地址
  size_t count;                     // 数据计数：操作的数据元素数量
  int root;                         // 根排名：对于有根操作（如 Broadcast）的根节点排名
  const char* datatype;             // 数据类型：NCCL 数据类型字符串（如 "ncclFloat32"）
  uint8_t nChannels;                // 通道数：此集合操作使用的通道数量
  const char* algo;                 // 算法：集合操作使用的算法名称（如 "RING", "TREE"）
  const char* proto;                // 协议：集合操作使用的协议名称（如 "LL", "LL128"）
  int nWarps;                       // Warp 数：此集合操作使用的 GPU warp 数量
  struct proxyOp op[MAX_CHANNELS][2*MAX_OPS]; // 代理操作数组：每个通道的代理操作（发送和接收）
  int nProxyOps[MAX_CHANNELS];      // 代理操作计数：每个通道的代理操作数量
  struct kernelCh kernel[MAX_CHANNELS]; // 内核事件数组：每个通道的内核事件
};

/**
 * @brief 点对点操作事件结构体
 *
 * 代表 NCCL 点对点操作（如 Send、Recv）的完整信息。
 * 相比集合操作更简单，主要涉及两个排名之间的直接通信。
 */
struct p2p {
  struct taskEventBase base;        // 基础结构：继承任务事件的基本属性
  uint8_t func;                     // 函数类型：点对点操作的具体类型（发送或接收）
  void const* buff;                 // 数据缓冲区：操作的数据内存地址
  size_t count;                     // 数据计数：操作的数据元素数量
  const char* datatype;             // 数据类型：NCCL 数据类型字符串
  int peer;                         // 对等排名：点对点操作的对等方排名
  uint8_t nChannels;                // 通道数：此点对点操作使用的通道数量
  struct proxyOp op[MAX_CHANNELS];  // 代理操作数组：每个通道的代理操作
  struct kernelCh kernel[MAX_CHANNELS]; // 内核事件数组：每个通道的内核事件
};

/**
 * @brief 组事件结构体
 *
 * 代表一组相关的 NCCL 操作。通常一个组包含一个或多个集合操作或点对点操作。
 * 组提供了操作的逻辑分组和生命周期管理。
 */
struct group {
  uint8_t type;                     // 事件类型：标识为组事件
  struct context* ctx;              // profiler 上下文：指向 profiler 上下文对象
  int groupId;                      // 组 ID：组的唯一标识符
  int refCount;                     // 引用计数：组的引用数量，用于生命周期管理
  struct taskEventBase* eventHead;  // 事件队列头：任务事件队列的头指针
  struct taskEventBase* eventTail;  // 事件队列尾：任务事件队列的尾指针
  double startTs;                   // 开始时间戳：组开始的时间
  double stopTs;                    // 结束时间戳：组结束的时间
  struct group* next;               // 下一个组：队列中下一个组事件的指针
};

/**
 * @brief Profiler 上下文结构体
 *
 * 管理 profiler 的全局状态和资源池。为不同类型的事件提供内存池管理，
 * 避免频繁的内存分配和释放，提高性能分析的效率。
 */
struct context {
  const char* commName;             // 通信器名称：用户分配的通信器名称
  uint64_t commHash;                // 通信器哈希：通信器的唯一标识符
  int nranks;                       // 排名总数：通信器中的排名数量
  int rank;                         // 当前排名：当前进程在通信器中的排名

  /* 组事件内存池管理 */
  int groupPoolSize;                // 组池大小：组事件内存池的总大小
  int groupPoolBase;                // 组池基址：组事件内存池的基础索引
  int groupPoolIndex;               // 组池索引：组事件内存池的当前索引
  struct group* groupPool;          // 组池指针：指向组事件内存池

  /* 集合操作事件内存池管理 */
  int collPoolSize;                 // 集合池大小：集合操作事件内存池的总大小
  int collPoolBase;                 // 集合池基址：集合操作事件内存池的基础索引
  int collPoolIndex;                // 集合池索引：集合操作事件内存池的当前索引
  struct collective* collPool;      // 集合池指针：指向集合操作事件内存池

  /* 点对点操作事件内存池管理 */
  int p2pPoolSize;                  // 点对点池大小：点对点操作事件内存池的总大小
  int p2pPoolBase;                  // 点对点池基址：点对点操作事件内存池的基础索引
  int p2pPoolIndex;                 // 点对点池索引：点对点操作事件内存池的当前索引
  struct p2p* p2pPool;              // 点对点池指针：指向点对点操作事件内存池

  /* 代理控制事件内存池管理 */
  int proxyCtrlPoolSize;            // 代理控制池大小：代理控制事件内存池的总大小
  int proxyCtrlPoolBase;            // 代理控制池基址：代理控制事件内存池的基础索引
  int proxyCtrlPoolIndex;           // 代理控制池索引：代理控制事件内存池的当前索引
  struct proxyCtrl* proxyCtrlPool;  // 代理控制池指针：指向代理控制事件内存池
};

/* 任务事件队列管理函数声明 */

/**
 * @brief 检查任务事件队列是否为空
 * @param g 组事件指针
 * @return 1 表示队列为空，0 表示队列非空
 */
int taskEventQueueEmpty(struct group* g);

/**
 * @brief 将任务事件加入队列
 * @param g 组事件指针
 * @param event 要加入队列的任务事件指针
 */
void taskEventQueueEnqueue(struct group* g, struct taskEventBase* event);

/**
 * @brief 获取任务事件队列的头部事件
 * @param g 组事件指针
 * @return 队列头部的任务事件指针，队列为空时返回 NULL
 */
struct taskEventBase* taskEventQueueHead(struct group* g);

/**
 * @brief 从任务事件队列中移除并返回头部事件
 * @param g 组事件指针
 * @return 被移除的任务事件指针，队列为空时返回 NULL
 */
struct taskEventBase* taskEventQueueDequeue(struct group* g);

#endif
