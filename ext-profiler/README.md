# NCCL Profiler Plugin Documentation

This page describes the NCCL Profiler plugin API and how to implement a profiler plugin for NCCL.

# Overview

To allow NCCL to better integrate with DL frameworks, NCCL v2.23 introduced a profiler plugin
interface. Any NCCL user can write profiler plugins to extract performance data from NCCL and
use it for debugging and analysis.

Similarly to other plugins (e.g., network plugin), the profiler plugins come as a shared library
called `libnccl-profiler.so`. That shared library contains one or more implementations of the
NCCL PROFILER API, in the form of versioned structs, filled with pointers to all required
functions.

# Plugin architecture

## Plugin name and supporting multiple profiler plugins

When NCCL is initialized, it will look for a `libnccl-profiler.so` library and dynamically load
it, then look for symbols inside the library.

The `NCCL_PROFILER_PLUGIN` environment variable allows multiple plugins to coexist. If set, NCCL
will look for a library with a name of `libnccl-profiler-${NCCL_PROFILER_PLUGIN}.so`. It is therefore
advised to name the library following that pattern, with a symlink pointing `libnccl-profiler.so`
to `libnccl-profiler-${NCCL_PROFILER_PLUGIN}.so`. That way, if there are multiple plugins in the
path, setting `NCCL_PROFILER_PLUGIN` will allow users to select the right plugin. Alternatively,
the user can also set `NCCL_PROFILER_PLUGIN` to the pathname of the `libnccl-profiler.so` library.

## Struct versioning

Once a library is found, NCCL will look for a symbol named `ncclProfiler_vX`, with `X` increasing
over time. The versioning ensures that the plugin and the NCCL core are compatible.

Plugins are encouraged to provide multiple of those symbols, implementing multiple versions of the
NCCL PROFILER API, so that the same plugin can be compiled and support a wide range of NCCL versions.

Conversely, and to ease transition, NCCL can choose to support different plugin versions, looking
for the latest ncclProfiler struct version, but also looking for older ones so that older plugins
would still work.

## Headers management

To help users build plugins effortlessly, plugins should copy the `ncclProfiler_vX` definitions
they support to their internal includes. An example is shown in `ext-profiler/example` where we
keep all headers in the `nccl/` directory and provide thin layers to implement old version on top
of newer ones.

The `nccl/` directory is populated with `profiler_vX.h` files extracting all relevant definitions
from old API versions. It also provides error codes in `err.h`.

# API (v4)

Below is the main `ncclProfiler_v4` struct. Each function is explained in later sections.

```
typedef struct {
  const char* name;

  // init - initialize the profiler plugin
  // Input
  //  - context        : opaque profiler context object for separating profiler behavior across comms
  //  - commName       : user assigned communicator name
  //  - commHash       : communicator id
  //  - nNodes         : number of nodes in communicator
  //  - nranks         : number of ranks in communicator
  //  - rank           : rank identifier in communicator
  //  - logfn          : logger function
  // Output
  //  - eActivationMask: bitmask of active events set by the plugin
  ncclResult_t (*init)(void** context, int* eActivationMask, const char* commName, uint64_t commHash, int nNodes, int nranks, int rank, ncclDebugLogger_t logfn);

  // startEvent - initialize and start a new event for the supplied event descriptor inside the eventset
  // Input
  //  - context: opaque profiler context object
  //  - eDescr : pointer to ncclProfilerEventDescr_t object
  // Output
  //  - eHandle: return event handle for supplied event descriptor object
  ncclResult_t (*startEvent)(void* context, void** eHandle, ncclProfilerEventDescr_v4_t* eDescr);

  // stopEvent - stop/finalize an event inside and event set
  // Input
  //  - eHandle: handle to event object
  ncclResult_t (*stopEvent)(void* eHandle);

  // recordEventState - record event state transitions and event attribute updates
  // Input
  //  - eHandle   : handle to event object created through startEvent
  //  - eStateArgs: optional argument used to capture event attribute updates associated with the state transition
  //  - eState    : event state transition
  ncclResult_t (*recordEventState)(void* eHandle, ncclProfilerEventState_v4_t eState, ncclProfilerEventStateArgs_v4_t* eStateArgs);

  // finalize - finalize the profiler plugin
  // Input
  //  - context: opaque profiler context object
  ncclResult_t (*finalize)(void* context);
} ncclProfiler_v4_t;
```

## Error codes

As rule of thumb, profiler generated errors should not be propagated to NCCL and alter its normal
functioning. Nevertheless, the profiler interface returns NCCL error codes, in case any need for
them arises in the future. For now, any profiler interface call should only return `ncclSuccess`.
The only exception is `init` that can return an error so that NCCL can disable the plugin.

## Operation overview

NCCL will call the `init` function first for every new communicator that is initialized. The profiler
returns an opaque context handle that is used to isolate profiler instances across communicators.
Similarly, NCCL will call `finalize` to destroy the profiler context, thus freeing resources.

The NCCL core code is instrumented with calls to `startEvent`, `stopEvent` and `recordEventState`.
These are used to start, stop and update events in the profiler, respectively.

## API Functions

### Initialization

#### name

The `name` field should point to a character string with the name of the profiler plugin. This will
be used for all logging, especially when `NCCL_DEBUG=INFO` is set.

#### init

As soon as NCCL finds the plugin and the correct ncclProfiler symbol, it calls its `init` function.
This allows the plugin to initialize its internal context, used during profiling of NCCL events.
If the `init` function does not return `ncclSuccess`, NCCL disables the plugin.

#### finalize

When the profiler is no longer needed, a call to `finalize` destroys the profiler context and frees
up resources.

### Profiling

#### startEvent

When NCCL needs to start profiling a new event it calls `startEvent`. `startEvent` takes the profiler
context, previously created by `init`, an event descriptor of type `ncclProfilerEventDescr_t` and
returns an opaque profiler event handle that can be passed to other profiler functions, as discussed
later in the document.


The event descriptor contains all the event metadata. Every event type has its own descriptor. Below
is the `ncclProfilerEventDescr_t` struct.

```
typedef struct {
  uint8_t type;             // event type (e.g., ncclProfileGroup, ncclProfileColl, ...)
  void* parentObj;          // pointer to parent event used to expose the event hierarchy to the profiler
  int rank;                 // rank that generated the event
  union {
    struct {                // collective events metadata
      uint64_t seqNumber;   // sequence number of this collective operation in the communicator
      const char* func;     // string containing name of the collective
      void const* sendBuff; // address of send buffer
      void* recvBuff;       // address of recv buffer
      size_t count;         // data count
      int root;             // root rank
      const char* datatype; // string containing the name of the datatype
      uint8_t nChannels;    // number of channels for this collective
      uint8_t nWarps;       // number of GPU warps for this collective
      const char* algo;     // string containing name of the algorithm for this collective
      const char* proto;    // string containing name of the protocol for this collective
    } coll;

    struct {                // point-to-point events metadata
      const char* func;
      void* buff;
      const char* datatype;
      size_t count;
      int peer;             // peer rank for this point-to-point
      uint8_t nChannels;    // number of channels for this p2p
    } p2p;

    struct {                // proxyOp events metadata
      pid_t pid;            // process id that generated the associated `ncclProxyOp` object
      uint8_t channelId;    // id of the channel used by the associated `ncclProxyOp` object
      int peer;             // peer rank
      int nSteps;           // number of network transfers/steps required by the `ncclProxyOp`
      int chunkSize;        // chunk size for this `ncclProxyOp`
      int isSend;           // type of network operation
    } proxyOp;

    struct {                // proxyStep events metadata
      int step;             // individual step in `ncclProxyOp`
    } proxyStep;

    struct {
      uint8_t channelId;    // id of the channel used by the kernel
      uint64_t ptimer;      // kernel supplied timestamp
    } kernelCh;

    struct {
      int64_t id;           // net plugin id (used by net and profiler plugins to agree on event definitions)
      void* data;           // pointer to network plugin defined event
    } netPlugin;
  };
} ncclProfilerEventDescr_v4_t;
```

NCCL defines the following events: `ncclProfileGroup`, `ncclProfileColl`, `ncclProfileP2p`,
`ncclProfileProxyOp`, `ncclProfileProxyStep`, `ncclProfileProxyCtrl`, `ncclProfileKernelCh` and
`ncclProfileNetPlugin`.

#### stopEvent

`stopEvent` takes the event handle returned by `startEvent` to stop the event. After the event
has been stopped the handle can no longer be used with other profiler calls. Using the event
handle after `eventStop` is undefined behavior.

#### recordEventState

Some events can only be started and stopped. For example, `ncclProfileGroup`, `ncclProfileColl`,
`ncclProfileP2p`, cannot be updated through calls to `recordEventState`.

`ncclProfileProxyOp`, `ncclProfileProxyStep`, `ncclProfileNetPlugin`, `ncclProfileKernelCh`, and
`ncclProfileProxyCtrl` can be updated through calls to `recordEventState`.

The state of these events can be updated, along with event attributes, using `recordEventState`.
These events can go through several states during their lifecycle.

The list of supported states for the updatable events is reported below.

```
typedef enum {
  // ncclProfileProxyOp event states
  ncclProfilerProxyOpSendPosted        = 0, // deprecated in v4
  ncclProfilerProxyOpSendRemFifoWait   = 1, // deprecated in v4
  ncclProfilerProxyOpSendTransmitted   = 2, // deprecated in v4
  ncclProfilerProxyOpSendDone          = 3, // deprecated in v4
  ncclProfilerProxyOpRecvPosted        = 4, // deprecated in v4
  ncclProfilerProxyOpRecvReceived      = 5, // deprecated in v4
  ncclProfilerProxyOpRecvTransmitted   = 6, // deprecated in v4
  ncclProfilerProxyOpRecvDone          = 7, // deprecated in v4
  ncclProfilerProxyOpInProgress_v4     = 19,// state marks transition of proxy op to progress

  // ncclProfileProxyStep event states
  ncclProfilerProxyStepSendGPUWait     = 8, // state marks the waiting of send data from GPU for given network transfer/step
  ncclProfilerProxyStepSendPeerWait_v4 = 20,// state marks the waiting of recv clear to send credits for given network transfer/step
  ncclProfilerProxyStepSendWait        = 9, // state marks the waiting of send data from network for given network transfer/step
  ncclProfilerProxyStepRecvWait        = 10,// state marks the waiting of recv data from network for given network transfer/step
  ncclProfilerProxyStepRecvFlushWait   = 11,// state marks the waiting of recv data flush to GPU for given network transfer/step
  ncclProfilerProxyStepRecvGPUWait     = 12,// state marks the waiting of recv data consumption from GPU for given network transfer/step

  // ncclProfileProxyCtrl event states
  ncclProfilerProxyCtrlIdle            = 13,// state marks proxy progress thread idle
  ncclProfilerProxyCtrlActive          = 14,// state marks proxy progress thread active
  ncclProfilerProxyCtrlSleep           = 15,// state marks proxy progress thread sleeping
  ncclProfilerProxyCtrlWakeup          = 16,// state marks proxy progress thread waking up
  ncclProfilerProxyCtrlAppend          = 17,// state marks append of new network work item begin
  ncclProfilerProxyCtrlAppendEnd       = 18,// state marks append of new network work item end

  // ncclProfileNetPlugin event states
  ncclProfilerNetPluginUpdate          = 21,// state marks update of network defined event

  // ncclProfileKernelCh event states
  ncclProfilerKernelChStop             = 22,// state marks stop of kernelCh event and timestamp update
} ncclProfilerEventState_v4_t;
```

`ncclProfileProxyOp` events are generated by the proxy progress thread while it is processing
network requests for the GPU kernel. ProxyOp events are generated for every active channel and
provide a summary of the activity of the proxy progress thread for that channel. Most of the
states for this event were duplicated with `ncclProfileProxyStep` events. Therefore, starting
with version 4 of the profiler interface these states have been deprecated. The same level of
information can still be obtained through the `ncclProfileProxyStep` events.

`ncclProfileProxyStep` events are generated by the proxy progress thread while it is processing
network requests for the GPU kernel. ProxyStep events describe individual network transfer in
the channel. Thus, they provide a more fine-grained view w.r.t. ProxyOp events.

`ncclProfileProxyCtrl` events are generated by the proxy progress thread while it is not processing
network requests for the GPU kernel. This includes everything else that the proxy thread might be
doing, including appending new `ncclProxyOp` objects to the list of work elements to process.

`ncclProfileKernelCh` events are generated by the profiler proxy progress function while the kernel
processes work items for the enqueued NCCL operations.

`ncclProfileNetPlugin` events are generated by the network plugin. Network plugins are free to define
their own set of events and communicate them to the profiler plugin using `ncclProfileNetPlugin` and
the `ncclProfilerCallback\_t` NCCL core callback. The network and profiler plugin can agree on the
network defined event definition using the plugin id in the event descriptor. The plugin identifier
is a 64-bit integer that has two parts: the 16 LSB are assigned to the plugin event version, the next
16 bits are assigned to the plugin type (NCCL\_PROFILER\_NET\_TYPE\_IB, ...). The rest of the bits are
unused and available for future extensions.

A network IB plugin can use this infrastructure to define a QP event as:

```C
#define NCCL_PROFILER_NET_IB_VER 1

enum {
  ncclProfileQp = (1 << 0),
};

// The data structure version is encoded in the plugin identifier bitmask and
// passed to NCCL core through the profiler callback. NCCL copies the plugin
// identifier in the event descriptor before calling the profiler startEvent
// function. The profiler should inspect the plugin id to find out the source
// plugin as well as the version of the event struct
typedef struct {
  uint8_t type;        // event type (plugin defined)
  union {
    struct {
      int device;      // network device id
      uint64_t wr_id;  // work request id
      int opcode;      // ibv opcode
      int qpNum;       // QP number
      size_t length;   // work request data length
    } qp;
  };
} ncclProfilerNetIbDescr_v1_t;
```

The network event infrastructure is network agnostic. A different network socket plugin can
use it to define a socket event as:

```C
#define NCCL_PROFILER_NET_SOCKET_VER 1

enum {
  ncclProfileSocket = (1 << 0),
};

// The data structure version is encoded in the plugin identifier bitmask and
// passed to NCCL core through the profiler callback. NCCL copies the plugin
// identifier in the event descriptor before calling the profiler startEvent
// function. The profiler should inspect the plugin id to find out the source
// plugin as well as the version of the event struct
typedef struct {
  uint8_t type;        // event type (plugin defined)
  union {
    struct {
      int fd;
      int op;
      size_t length;
    } sock;
  };
} ncclProfilerNetSockDescr_v1_t;
```

The network plugin creates an event (descriptor) and passes it to the profiler callback,
along with the network type and version (plugin id). NCCL then creates a `ncclProfileNetPlugin`
event descriptor, attaches the network plugin defined event as external data, and calls
the profiler `startEvent` function.

```C
ncclResult_t isend(..., void* phandle, ...) {
  ...
  int pluginId = NCCL_PROFILER_NET_TYPE_IB | NCCL_PROFILER_NET_IB_VER;
  ncclProfilerNetIbDescr_v1_t eDescr = { };
  eDescr.type = ncclProfileQp;
  eDescr.qp = { ... };
  ncclProfilerCallback(&eHandle, 0 /* start net event */, phandle, pluginId, &eDescr);
  ...
}
```

State transitions for the events described can also come with event attribute updates. For this
reason the profiler defines the `ncclProfilerEventStateArgs_t` struct, reported below.

```
typedef union {
  struct {                // attributes for update for ncclProfileProxyStep events
    size_t transSize;     // transfer size field for this proxy step
  } proxyStep;

  struct {                // attributes to update for ncclProfileProxyCtrl events
    int appendedProxyOps; // number of appended proxy ops thus far
  } proxyCtrl;

  struct {                // attributes to update for ncclProfileNetPlugin events
    void* data;           // network plugin opaque update data field
  } netPlugin;

  struct {                // attribute to update for ncclProfileKernelCh events
    uint64_t pTimer;      // timestamp provided by the NCCL kernel
  } kernelCh;
} ncclProfilerEventStateArgs_v4_t;
```

The example profiler in `ext-profiler/example` contains details on how to capture and use the events above.

### Event hierarchy

NCCL core events (reported above) are organized into a hierarchy as reported below:

```
Group event
   |
   +- Collective event
   |  |
   |  +- ProxyOp event
   |  |  |
   |  |  +- ProxyStep event
   |  |     |
   |  |     +- NetPlugin event
   |  |
   |  +- KernelCh event
   |
   +- Point-to-point event
      |
      +- ProxyOp event
      |  |
      |  +- ProxyStep event
      |     |
      |     +- NetPlugin event
      |
      +- KernelCh event

ProxyCtrl event
```

# Profiler instrumentation and logging

## Profiling of collective and p2p operations

The NCCL code is instrumented with profiler callbacks at different levels to capture start/stop of groups,
collective and point-to-point operations, as well as proxy, kernel and network activity. Due to the asynchronous nature
of NCCL operations, events associated to collective and point-to-point operations are not easy to delimit
precisely. For example, without both proxy and/or kernel activity it is impossible for the profiler to
figure out when a collective operation completes. Therefore, `stopEvent` for collectives simply indicates to
the profiler that the collective has been enqueued. The profiler can leverage proxy and/or kernel event information, if
these are enabled, to estimate when the collective ends. For example, the profiler can look at the `stopEvent`
call of the last `ncclProfileProxyOp` event to mark the completion of the associated collective event. This
can be achieved by reference counting the collective event and letting calls to `startEvent` and `stopEvent`
increment and decrement the reference counter, respectively.

## PXN

PXN causes some proxy operations to be processed in a remote proxy thread that differs from the one that
generated the operation. When this happens, the event hierarchy reported above breaks. Because the
profiler can use the hierarchy information, provided by NCCL in the event descriptor, to dereference the
parent event during `startEvent`, the remote proxy thread must be in the same address space of the proxy
thread originating the operation. To avoid the profiler instance in the remote proxy address space to
dereference a pointer from another address space the event descriptor includes the PID of the originator.
The profiler plugin needs to check that the originator PID matches the local PID before dereferencing the
parent event.

# Known Limitations

In intra-node communication, or whenever a rank does not have any network activity for which proxy events
are unavailable, the profiler will only report the enqueue events (e.g., ncclAllReduce). The events from
enqueue can be time stamped by the profiler (at start and stop) to reconstruct the execution time of the
collective. However, this time only represents the launch time of the collective and not the actual
execution time. To reconstruct the execution time more accurately proxy and kernel events are provided.

With version 3 of the profiler interface network activity is no longer required to do intra-node profiling.
Kernel events instrumentation leverages counters exposed by the kernel to the host and the proxy progress
thread. Thus, the proxy progress thread infrastructure is shared between the network and the profiler. If
the proxy is serving network requests the kernel profiling probing can be delayed, causing loss of
accuracy. Similarly, if the CPU is under heavy load and the scheduling of the proxy progress thread is
delayed, a similar loss of accuracy can be encountered.

To mitigate this effect, with version 4 of the profiler NCCL uses a per-channel ring buffer of 64 elements.
Every counter is complemented by two timestamps (ptimers) supplied by the NCCL kernel (one for start and one
for stop of the operation in the kernel). NCCL propagates these timestamps to the profiler plugin that it can
convert them to CPU time domain.

---

# 中文翻译

# NCCL Profiler 插件文档

本页面描述了 NCCL Profiler 插件 API 以及如何为 NCCL 实现 profiler 插件。

# 概述

为了让 NCCL 更好地与深度学习框架集成，NCCL v2.23 引入了 profiler 插件接口。任何 NCCL 用户都可以编写 profiler 插件来从 NCCL 中提取性能数据，并将其用于调试和分析。

与其他插件（例如网络插件）类似，profiler 插件以名为 `libnccl-profiler.so` 的共享库形式提供。该共享库包含一个或多个 NCCL PROFILER API 的实现，以版本化结构体的形式，填充了指向所有必需函数的指针。

# 插件架构

## 插件名称和支持多个 profiler 插件

当 NCCL 初始化时，它会查找 `libnccl-profiler.so` 库并动态加载它，然后在库内查找符号。

`NCCL_PROFILER_PLUGIN` 环境变量允许多个插件共存。如果设置了该变量，NCCL 将查找名为 `libnccl-profiler-${NCCL_PROFILER_PLUGIN}.so` 的库。因此建议按照该模式命名库，并使用符号链接将 `libnccl-profiler.so` 指向 `libnccl-profiler-${NCCL_PROFILER_PLUGIN}.so`。这样，如果路径中有多个插件，设置 `NCCL_PROFILER_PLUGIN` 将允许用户选择正确的插件。或者，用户也可以将 `NCCL_PROFILER_PLUGIN` 设置为 `libnccl-profiler.so` 库的路径名。

## 结构体版本控制

找到库后，NCCL 将查找名为 `ncclProfiler_vX` 的符号，其中 `X` 随时间递增。版本控制确保插件和 NCCL 核心兼容。

鼓励插件提供多个这样的符号，实现 NCCL PROFILER API 的多个版本，这样同一个插件可以编译并支持广泛的 NCCL 版本。

相反，为了简化过渡，NCCL 可以选择支持不同的插件版本，查找最新的 ncclProfiler 结构体版本，但也查找较旧的版本，以便较旧的插件仍然可以工作。

## 头文件管理

为了帮助用户轻松构建插件，插件应该将它们支持的 `ncclProfiler_vX` 定义复制到它们的内部包含文件中。在 `ext-profiler/example` 中显示了一个示例，我们将所有头文件保存在 `nccl/` 目录中，并提供薄层来在较新版本之上实现旧版本。

`nccl/` 目录填充了 `profiler_vX.h` 文件，从旧 API 版本中提取所有相关定义。它还在 `err.h` 中提供错误代码。

# API (v4)

下面是主要的 `ncclProfiler_v4` 结构体。每个函数在后面的章节中解释。

```
typedef struct {
  const char* name;

  // init - 初始化 profiler 插件
  // 输入
  //  - context        : 用于在通信器之间分离 profiler 行为的不透明 profiler 上下文对象
  //  - commName       : 用户分配的通信器名称
  //  - commHash       : 通信器 ID
  //  - nNodes         : 通信器中的节点数
  //  - nranks         : 通信器中的排名数
  //  - rank           : 通信器中的排名标识符
  //  - logfn          : 日志记录函数
  // 输出
  //  - eActivationMask: 插件设置的活动事件位掩码
  ncclResult_t (*init)(void** context, int* eActivationMask, const char* commName, uint64_t commHash, int nNodes, int nranks, int rank, ncclDebugLogger_t logfn);

  // startEvent - 为事件集内提供的事件描述符初始化并启动新事件
  // 输入
  //  - context: 不透明 profiler 上下文对象
  //  - eDescr : 指向 ncclProfilerEventDescr_t 对象的指针
  // 输出
  //  - eHandle: 为提供的事件描述符对象返回事件句柄
  ncclResult_t (*startEvent)(void* context, void** eHandle, ncclProfilerEventDescr_v4_t* eDescr);

  // stopEvent - 停止/完成事件集内的事件
  // 输入
  //  - eHandle: 事件对象的句柄
  ncclResult_t (*stopEvent)(void* eHandle);

  // recordEventState - 记录事件状态转换和事件属性更新
  // 输入
  //  - eHandle   : 通过 startEvent 创建的事件对象句柄
  //  - eStateArgs: 用于捕获与状态转换相关的事件属性更新的可选参数
  //  - eState    : 事件状态转换
  ncclResult_t (*recordEventState)(void* eHandle, ncclProfilerEventState_v4_t eState, ncclProfilerEventStateArgs_v4_t* eStateArgs);

  // finalize - 完成 profiler 插件
  // 输入
  //  - context: 不透明 profiler 上下文对象
  ncclResult_t (*finalize)(void* context);
} ncclProfiler_v4_t;
```

## 错误代码

作为经验法则，profiler 生成的错误不应传播到 NCCL 并改变其正常功能。尽管如此，profiler 接口返回 NCCL 错误代码，以防将来需要它们。目前，任何 profiler 接口调用都应该只返回 `ncclSuccess`。唯一的例外是 `init`，它可以返回错误，以便 NCCL 可以禁用插件。

## 操作概述

NCCL 将首先为每个初始化的新通信器调用 `init` 函数。profiler 返回一个不透明的上下文句柄，用于在通信器之间隔离 profiler 实例。类似地，NCCL 将调用 `finalize` 来销毁 profiler 上下文，从而释放资源。

NCCL 核心代码使用对 `startEvent`、`stopEvent` 和 `recordEventState` 的调用进行检测。这些分别用于在 profiler 中启动、停止和更新事件。

## API 函数

### 初始化

#### name

`name` 字段应指向包含 profiler 插件名称的字符串。这将用于所有日志记录，特别是当设置 `NCCL_DEBUG=INFO` 时。

#### init

一旦 NCCL 找到插件和正确的 ncclProfiler 符号，它就会调用其 `init` 函数。这允许插件初始化其内部上下文，在 NCCL 事件的性能分析期间使用。如果 `init` 函数不返回 `ncclSuccess`，NCCL 将禁用插件。

#### finalize

当不再需要 profiler 时，调用 `finalize` 会销毁 profiler 上下文并释放资源。

### 性能分析

#### startEvent

当 NCCL 需要开始分析新事件时，它调用 `startEvent`。`startEvent` 接受先前由 `init` 创建的 profiler 上下文、类型为 `ncclProfilerEventDescr_t` 的事件描述符，并返回一个不透明的 profiler 事件句柄，该句柄可以传递给其他 profiler 函数，如本文档后面所讨论的。

事件描述符包含所有事件元数据。每种事件类型都有自己的描述符。下面是 `ncclProfilerEventDescr_t` 结构体。

```
typedef struct {
  uint8_t type;             // 事件类型 (例如, ncclProfileGroup, ncclProfileColl, ...)
  void* parentObj;          // 指向父事件的指针，用于向 profiler 公开事件层次结构
  int rank;                 // 生成事件的排名
  union {
    struct {                // 集合事件元数据
      uint64_t seqNumber;   // 此集合操作在通信器中的序列号
      const char* func;     // 包含集合名称的字符串
      void const* sendBuff; // 发送缓冲区地址
      void* recvBuff;       // 接收缓冲区地址
      size_t count;         // 数据计数
      int root;             // 根排名
      const char* datatype; // 包含数据类型名称的字符串
      uint8_t nChannels;    // 此集合的通道数
      uint8_t nWarps;       // 此集合的 GPU warp 数
      const char* algo;     // 包含此集合算法名称的字符串
      const char* proto;    // 包含此集合协议名称的字符串
    } coll;

    struct {                // 点对点事件元数据
      const char* func;
      void* buff;
      const char* datatype;
      size_t count;
      int peer;             // 此点对点的对等排名
      uint8_t nChannels;    // 此 p2p 的通道数
    } p2p;

    struct {                // proxyOp 事件元数据
      pid_t pid;            // 生成关联 `ncclProxyOp` 对象的进程 ID
      uint8_t channelId;    // 关联 `ncclProxyOp` 对象使用的通道 ID
      int peer;             // 对等排名
      int nSteps;           // `ncclProxyOp` 所需的网络传输/步骤数
      int chunkSize;        // 此 `ncclProxyOp` 的块大小
      int isSend;           // 网络操作类型
    } proxyOp;

    struct {                // proxyStep 事件元数据
      int step;             // `ncclProxyOp` 中的单个步骤
    } proxyStep;

    struct {
      uint8_t channelId;    // 内核使用的通道 ID
      uint64_t ptimer;      // 内核提供的时间戳
    } kernelCh;

    struct {
      int64_t id;           // 网络插件 ID（网络和 profiler 插件用于就事件定义达成一致）
      void* data;           // 指向网络插件定义事件的指针
    } netPlugin;
  };
} ncclProfilerEventDescr_v4_t;
```

NCCL 定义了以下事件：`ncclProfileGroup`、`ncclProfileColl`、`ncclProfileP2p`、`ncclProfileProxyOp`、`ncclProfileProxyStep`、`ncclProfileProxyCtrl`、`ncclProfileKernelCh` 和 `ncclProfileNetPlugin`。

#### stopEvent

`stopEvent` 接受 `startEvent` 返回的事件句柄来停止事件。事件停止后，句柄不能再与其他 profiler 调用一起使用。在 `eventStop` 后使用事件句柄是未定义的行为。

#### recordEventState

某些事件只能启动和停止。例如，`ncclProfileGroup`、`ncclProfileColl`、`ncclProfileP2p` 不能通过调用 `recordEventState` 进行更新。

`ncclProfileProxyOp`、`ncclProfileProxyStep`、`ncclProfileNetPlugin`、`ncclProfileKernelCh` 和 `ncclProfileProxyCtrl` 可以通过调用 `recordEventState` 进行更新。

这些事件的状态可以使用 `recordEventState` 与事件属性一起更新。这些事件在其生命周期中可以经历几个状态。

下面报告了可更新事件支持的状态列表。

```
typedef enum {
  // ncclProfileProxyOp 事件状态
  ncclProfilerProxyOpSendPosted        = 0, // 在 v4 中已弃用
  ncclProfilerProxyOpSendRemFifoWait   = 1, // 在 v4 中已弃用
  ncclProfilerProxyOpSendTransmitted   = 2, // 在 v4 中已弃用
  ncclProfilerProxyOpSendDone          = 3, // 在 v4 中已弃用
  ncclProfilerProxyOpRecvPosted        = 4, // 在 v4 中已弃用
  ncclProfilerProxyOpRecvReceived      = 5, // 在 v4 中已弃用
  ncclProfilerProxyOpRecvTransmitted   = 6, // 在 v4 中已弃用
  ncclProfilerProxyOpRecvDone          = 7, // 在 v4 中已弃用
  ncclProfilerProxyOpInProgress_v4     = 19,// 状态标记代理操作转换为进行中

  // ncclProfileProxyStep 事件状态
  ncclProfilerProxyStepSendGPUWait     = 8, // 状态标记等待给定网络传输/步骤的 GPU 发送数据
  ncclProfilerProxyStepSendPeerWait_v4 = 20,// 状态标记等待给定网络传输/步骤的接收清除发送信用
  ncclProfilerProxyStepSendWait        = 9, // 状态标记等待给定网络传输/步骤的网络发送数据
  ncclProfilerProxyStepRecvWait        = 10,// 状态标记等待给定网络传输/步骤的网络接收数据
  ncclProfilerProxyStepRecvFlushWait   = 11,// 状态标记等待给定网络传输/步骤的接收数据刷新到 GPU
  ncclProfilerProxyStepRecvGPUWait     = 12,// 状态标记等待给定网络传输/步骤的 GPU 接收数据消费

  // ncclProfileProxyCtrl 事件状态
  ncclProfilerProxyCtrlIdle            = 13,// 状态标记代理进度线程空闲
  ncclProfilerProxyCtrlActive          = 14,// 状态标记代理进度线程活动
  ncclProfilerProxyCtrlSleep           = 15,// 状态标记代理进度线程睡眠
  ncclProfilerProxyCtrlWakeup          = 16,// 状态标记代理进度线程唤醒
  ncclProfilerProxyCtrlAppend          = 17,// 状态标记新网络工作项追加开始
  ncclProfilerProxyCtrlAppendEnd       = 18,// 状态标记新网络工作项追加结束

  // ncclProfileNetPlugin 事件状态
  ncclProfilerNetPluginUpdate          = 21,// 状态标记网络定义事件的更新

  // ncclProfileKernelCh 事件状态
  ncclProfilerKernelChStop             = 22,// 状态标记 kernelCh 事件停止和时间戳更新
} ncclProfilerEventState_v4_t;
```

`ncclProfileProxyOp` 事件由代理进度线程在处理 GPU 内核的网络请求时生成。ProxyOp 事件为每个活动通道生成，并提供该通道代理进度线程活动的摘要。此事件的大多数状态与 `ncclProfileProxyStep` 事件重复。因此，从 profiler 接口版本 4 开始，这些状态已被弃用。仍然可以通过 `ncclProfileProxyStep` 事件获得相同级别的信息。

`ncclProfileProxyStep` 事件由代理进度线程在处理 GPU 内核的网络请求时生成。ProxyStep 事件描述通道中的单个网络传输。因此，相对于 ProxyOp 事件，它们提供了更细粒度的视图。

`ncclProfileProxyCtrl` 事件由代理进度线程在不处理 GPU 内核的网络请求时生成。这包括代理线程可能正在做的其他所有事情，包括将新的 `ncclProxyOp` 对象追加到要处理的工作元素列表中。

`ncclProfileKernelCh` 事件由 profiler 代理进度函数在内核处理排队的 NCCL 操作的工作项时生成。

`ncclProfileNetPlugin` 事件由网络插件生成。网络插件可以自由定义自己的事件集，并使用 `ncclProfileNetPlugin` 和 `ncclProfilerCallback_t` NCCL 核心回调将它们传达给 profiler 插件。网络和 profiler 插件可以使用事件描述符中的插件 ID 就网络定义的事件定义达成一致。插件标识符是一个 64 位整数，有两部分：16 个 LSB 分配给插件事件版本，接下来的 16 位分配给插件类型（NCCL_PROFILER_NET_TYPE_IB，...）。其余位未使用，可用于将来的扩展。

网络 IB 插件可以使用此基础设施将 QP 事件定义为：

```C
#define NCCL_PROFILER_NET_IB_VER 1

enum {
  ncclProfileQp = (1 << 0),
};

// 数据结构版本编码在插件标识符位掩码中，并通过 profiler 回调传递给 NCCL 核心。
// NCCL 在调用 profiler startEvent 函数之前将插件标识符复制到事件描述符中。
// profiler 应该检查插件 ID 以找出源插件以及事件结构的版本
typedef struct {
  uint8_t type;        // 事件类型（插件定义）
  union {
    struct {
      int device;      // 网络设备 ID
      uint64_t wr_id;  // 工作请求 ID
      int opcode;      // ibv 操作码
      int qpNum;       // QP 号码
      size_t length;   // 工作请求数据长度
    } qp;
  };
} ncclProfilerNetIbDescr_v1_t;
```

网络事件基础设施是网络无关的。不同的网络套接字插件可以使用它来定义套接字事件：

```C
#define NCCL_PROFILER_NET_SOCKET_VER 1

enum {
  ncclProfileSocket = (1 << 0),
};

// 数据结构版本编码在插件标识符位掩码中，并通过 profiler 回调传递给 NCCL 核心。
// NCCL 在调用 profiler startEvent 函数之前将插件标识符复制到事件描述符中。
// profiler 应该检查插件 ID 以找出源插件以及事件结构的版本
typedef struct {
  uint8_t type;        // 事件类型（插件定义）
  union {
    struct {
      int fd;
      int op;
      size_t length;
    } sock;
  };
} ncclProfilerNetSockDescr_v1_t;
```

网络插件创建一个事件（描述符）并将其传递给 profiler 回调，以及网络类型和版本（插件 ID）。然后 NCCL 创建一个 `ncclProfileNetPlugin` 事件描述符，将网络插件定义的事件作为外部数据附加，并调用 profiler `startEvent` 函数。

```C
ncclResult_t isend(..., void* phandle, ...) {
  ...
  int pluginId = NCCL_PROFILER_NET_TYPE_IB | NCCL_PROFILER_NET_IB_VER;
  ncclProfilerNetIbDescr_v1_t eDescr = { };
  eDescr.type = ncclProfileQp;
  eDescr.qp = { ... };
  ncclProfilerCallback(&eHandle, 0 /* start net event */, phandle, pluginId, &eDescr);
  ...
}
```

所描述事件的状态转换也可以伴随事件属性更新。因此，profiler 定义了 `ncclProfilerEventStateArgs_t` 结构体，如下所示。

```
typedef union {
  struct {                // ncclProfileProxyStep 事件更新的属性
    size_t transSize;     // 此代理步骤的传输大小字段
  } proxyStep;

  struct {                // ncclProfileProxyCtrl 事件更新的属性
    int appendedProxyOps; // 到目前为止追加的代理操作数
  } proxyCtrl;

  struct {                // ncclProfileNetPlugin 事件更新的属性
    void* data;           // 网络插件不透明更新数据字段
  } netPlugin;

  struct {                // ncclProfileKernelCh 事件更新的属性
    uint64_t pTimer;      // NCCL 内核提供的时间戳
  } kernelCh;
} ncclProfilerEventStateArgs_v4_t;
```

`ext-profiler/example` 中的示例 profiler 包含如何捕获和使用上述事件的详细信息。

### 事件层次结构

NCCL 核心事件（如上所述）组织成如下所示的层次结构：

```
Group 事件
   |
   +- Collective 事件
   |  |
   |  +- ProxyOp 事件
   |  |  |
   |  |  +- ProxyStep 事件
   |  |     |
   |  |     +- NetPlugin 事件
   |  |
   |  +- KernelCh 事件
   |
   +- Point-to-point 事件
      |
      +- ProxyOp 事件
      |  |
      |  +- ProxyStep 事件
      |     |
      |     +- NetPlugin 事件
      |
      +- KernelCh 事件

ProxyCtrl 事件
```

# Profiler 检测和日志记录

## 集合操作和点对点操作的性能分析

NCCL 代码在不同级别使用 profiler 回调进行检测，以捕获组、集合操作和点对点操作的启动/停止，以及代理、内核和网络活动。由于 NCCL 操作的异步性质，与集合操作和点对点操作相关的事件不容易精确界定。例如，没有代理和/或内核活动，profiler 无法确定集合操作何时完成。因此，集合操作的 `stopEvent` 只是向 profiler 指示集合操作已入队。如果启用了代理和/或内核事件信息，profiler 可以利用这些信息来估计集合操作何时结束。例如，profiler 可以查看最后一个 `ncclProfileProxyOp` 事件的 `stopEvent` 调用来标记相关集合事件的完成。这可以通过对集合事件进行引用计数并让对 `startEvent` 和 `stopEvent` 的调用分别递增和递减引用计数器来实现。

## PXN

PXN 导致某些代理操作在与生成操作的代理线程不同的远程代理线程中处理。当这种情况发生时，上面报告的事件层次结构会被破坏。因为 profiler 可以使用 NCCL 在事件描述符中提供的层次结构信息在 `startEvent` 期间解引用父事件，所以远程代理线程必须与发起操作的代理线程在同一地址空间中。为了避免远程代理地址空间中的 profiler 实例解引用来自另一个地址空间的指针，事件描述符包括发起者的 PID。profiler 插件需要在解引用父事件之前检查发起者 PID 是否与本地 PID 匹配。

# 已知限制

在节点内通信中，或者当排名没有任何网络活动且代理事件不可用时，profiler 将只报告入队事件（例如，ncclAllReduce）。入队事件可以由 profiler 进行时间戳记录（在开始和停止时）以重建集合操作的执行时间。但是，这个时间只代表集合操作的启动时间，而不是实际的执行时间。为了更准确地重建执行时间，提供了代理和内核事件。

使用 profiler 接口版本 3，不再需要网络活动来进行节点内性能分析。内核事件检测利用内核向主机和代理进度线程公开的计数器。因此，代理进度线程基础设施在网络和 profiler 之间共享。如果代理正在服务网络请求，内核性能分析探测可能会延迟，导致精度损失。类似地，如果 CPU 负载很重且代理进度线程的调度延迟，也可能遇到类似的精度损失。

为了减轻这种影响，profiler 版本 4 中 NCCL 使用每通道 64 个元素的环形缓冲区。每个计数器都由 NCCL 内核提供的两个时间戳（ptimers）补充（一个用于内核中操作的开始，一个用于停止）。NCCL 将这些时间戳传播到 profiler 插件，插件可以将它们转换为 CPU 时间域。

`ncclProfileProxyOp` 事件由代理进度线程在处理 GPU 内核的网络请求时生成。ProxyOp 事件为每个活动通道生成，并提供该通道代理进度线程活动的摘要。此事件的大多数状态与 `ncclProfileProxyStep` 事件重复。因此，从 profiler 接口版本 4 开始，这些状态已被弃用。仍然可以通过 `ncclProfileProxyStep` 事件获得相同级别的信息。

`ncclProfileProxyStep` 事件由代理进度线程在处理 GPU 内核的网络请求时生成。ProxyStep 事件描述通道中的单个网络传输。因此，相对于 ProxyOp 事件，它们提供了更细粒度的视图。

`ncclProfileProxyCtrl` 事件由代理进度线程在不处理 GPU 内核的网络请求时生成。这包括代理线程可能正在做的其他所有事情，包括将新的 `ncclProxyOp` 对象追加到要处理的工作元素列表中。

`ncclProfileKernelCh` 事件由 profiler 代理进度函数在内核处理排队的 NCCL 操作的工作项时生成。

`ncclProfileNetPlugin` 事件由网络插件生成。网络插件可以自由定义自己的事件集，并使用 `ncclProfileNetPlugin` 和 `ncclProfilerCallback_t` NCCL 核心回调将它们传达给 profiler 插件。网络和 profiler 插件可以使用事件描述符中的插件 ID 就网络定义的事件定义达成一致。插件标识符是一个 64 位整数，有两部分：16 个 LSB 分配给插件事件版本，接下来的 16 位分配给插件类型（NCCL_PROFILER_NET_TYPE_IB，...）。其余位未使用，可用于将来的扩展。
