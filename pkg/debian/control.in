Source: nccl
Section: libs
Maintainer: cudatools <<EMAIL>>
Priority: optional
Build-depends: debhelper(>=9)
Standards-Version: 3.9.5

Package: libnccl${nccl:Major}
Section: libs
Architecture: ${pkg:Arch}
Depends: ${misc:Depends}, ${shlibs:Depends}
Description: NVIDIA Collective Communication Library (NCCL) Runtime
 NCCL (pronounced "Nickel") is a stand-alone library of standard collective
 communication routines for GPUs, implementing all-reduce, all-gather, reduce,
 broadcast, and reduce-scatter.
 It has been optimized to achieve high bandwidth on any platform using PCIe,
 NVLink, NVswitch, as well as networking using InfiniBand Verbs or TCP/IP
 sockets.

Package: libnccl-dev
Section: libdevel
Architecture: ${pkg:Arch}
Depends: ${misc:Depends}, ${shlibs:Depends}, libnccl${nccl:Major} (= ${binary:Version})
Description: NVIDIA Collective Communication Library (NCCL) Development Files
 NCCL (pronounced "<PERSON><PERSON>") is a stand-alone library of standard collective
 communication routines for GPUs, implementing all-reduce, all-gather, reduce,
 broadcast, and reduce-scatter.
 It has been optimized to achieve high bandwidth on any platform using PCIe,
 NVLink, NVswitch, as well as networking using InfiniBand Verbs or TCP/IP
 sockets.
